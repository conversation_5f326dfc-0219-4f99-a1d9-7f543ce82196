# Generate Census 2021
This document is to explan how to generate census 2021 files, if you already have files (from rmlfs) and just want to import it, please reference `docs/Dev_onlineSteps/20230119-import-census2021.md`.
## Boundary

### Download boundary file
Download from Statistics Canada -> Census -> Census of Population -> Census geography -> Spatial information products Boundary files
https://www12.statcan.gc.ca/census-recensement/2021/geo/sip-pis/boundary-limites/index2021-eng.cfm?year=21
 
Choose Statistical boundaries -> Dissemination areas, download Shapefile (.shp) format for boundary

### Convert boundary file format
use QGIS change the shp file to GeoJson.
steps:
  1. Open .shp file
  2. In menu, Layer, -> Save As...
  3. File name: gmlbounday.geojson
  4. CRS: Default CRS: EPSG4326 - WGS 84
  5. Save file

### Import Boundary
1. import boundary files
./start.sh batch/boundary/importBoundaryV2.coffee census2021 force /pathToFolder/

2. 边界如果有交叉，建立geo spacial index时会出错，錯誤的會存到 sensus2021Error，手动调整 error 的 boundary，存到 census2021 collection. 


## Census

### Download Census file
Download from Statistics Canada -> Census -> Census of Population -> Data products, 2021 Census -> Census Profile, 2021 Census of Population
https://www12.statcan.gc.ca/census-recensement/2021/dp-pd/prof/details/download-telecharger.cfm?Lang=E

Choose Comprehensive download files -> Canada, provinces, territories, census divisions (CDs), census subdivisions (CSDs) and dissemination areas (DAs) -> CSV

### Process and Import Census file

1. Prepare `meta_2021.csv` and `notes_2021.csv`, already provided in `src/batch/census2021/meta_2021.csv`, those contents are copy from downloaded census file `98-401-X2021006_English_meta.txt`

2. import keys
```
./start.sh lib/batchBase.coffee batch/census2021/processKeys.coffee
```

2. import notes
```
./start.sh lib/batchBase.coffee batch/census2021/processNotes.coffee
```

3. import census data for each province
```
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_Atlantic.csv
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_BritishColumbia.csv
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_Ontario.csv
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_Prairies.csv 
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_Quebec.csv 
./start.sh lib/batchBase.coffee batch/census2021/processCensus.coffee /pathToCensusFolder/98-401-X2021006_English_CSV_data_Territories.csv 
```

### Create Census Map Image
Create and save image
```
./start.sh lib/batchBase.coffee batch/census2021/createCensusMapImage.coffee preload-model
```

Add picBbox field to census
```
./start.sh lib/batchBase.coffee batch/census2021/addPicBbox.coffee preload-model
```