#Migrate Procedures:
1. (Details below)setup image server. ./starts.coffee
2. setup image server DNS
3. aws s3 sync to image server
4. set config.coffee: disableUpload: true, restart nodejs on all servers @11
5. aws s3 sync to image server
6. at db, run run ./start.sh batch/user/userFile.coffee, update user file, (v4 already supported)this will change fld->ufld, wwwbase->fldr, base-> remove http
7. (Optional) at appServer run ./start.sh batch/syncUserImgs(sync), this will do following
  * remove user file record that dont exists on s3 server
  * remove thumb file that has no parent (eg. /G/T/\_A.jpg but /G/T/A.jpg is gone)
  * add s3 existed file to user
<!-- 4. at appServer modify _rootPath_ to correct folder, run fetch userImages.coffee, this will do following
  * ready user_file db, stream user files and download user images to folder _rootPath_ -->
8. run aws sync at imgServer(download all imgs from s3,   80G/2.5hour, 2G/20mins)
<!-- 5. copy _rootPath_ to image server(or run download all in file server, s3) -->
9. update appServer to upload branch(v4) config as @11, restart all servers
10. modify DNS f.realmaster.com -> file server
11. if anything goes wrong, no more use base, change dns and fix bug
<!-- #appServer base to old base, -->

#Migrate user base:
1. ./start.sh batch/user/userFile.coffee
2. to use https, change config.user_file.protocol http -> https
3. to use diff base, change config.user_file.wwwbase

#Image Server Config: pull imgd1
1. vhost.yml
      fileserver:
        match:
          hostname: fu\.t\.realmaster\.cn
        setting:
          path_root: <%this.srcPath%>/webroot/public
          path_upload: /tmp
          site: ImageFileServer
2. base.yml
      server port
      filePath
      fileHost
3. config.coffee apps use /appImgServer
4. config.coffee
      + publicImgFilePath: '/mnt/files/uploaded/'
      + publicImgErrorFile: '<%this.srcPath%>/s3uploadError.log'
      + imgServerDlAddr: 'http://fs.t.realmaster.cn'
      + imgServerUlAddr: 'http://fu.t.realmaster.cn'
      + moveFile: copy/rename

<!-- NOTE: orig purpose of useBase, but have to maintain multiple servers, depreciated -->
<!-- # wepage, http://f.realmaster.com/G/T/A.png -> s3 -->
<!-- # wwwbase2, http://fs.realmaster.com/G/T/A.png -> img server -->

# @11 App Server config:
1. config.coffee
      + imgServerUlAddr: 'http://fu.t.realmaster.cn'
      user_file:
        + wwwbase: '<%this.s3.bucket%>'
        folder: 'G'
        + protocol: 'http'
        + disableUpload: false


# sync(download) all s3 imgs
# https://docs.aws.amazon.com/cli/latest/index.html
```
yum install python-pip
sudo easy_install awscli or sudo pip install awscli
aws s3 sync s3://mybucket .
```
Output:
```
download: s3://mybucket/test.txt to test.txt
download: s3://mybucket/test2.txt to test2.txt
```