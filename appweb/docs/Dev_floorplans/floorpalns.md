# Floorplans 结构与batch说明

## description

1. 给condos.floorplan图片添加水印，并将floorplan数据移至数据库vow.floorplans(batch:src/batch/floorPlanWaterMark.coffee)
2. 具体需求见 docs/Change_requirements/20240523_fix_floorplan.md

## floorplans  collection

| field      | data type      | description
| ---------- | -------------- | ---------------------------------
| _id        | string         | uuid
| tp         | string         | 区分img为floorplan或building
| uaddr      | string         | uaddr, condos->id
| src        | string         | source, 区分mls上传, 人工上传(manu), 已经有floorplan解析导入进来(import)
| prov       | string         | province
| city       | string         | city
| nm         | string         | name, condos.floorplan 相同name表明是同房源数据(loft类型等, 多张户型图)
| bdrms      | string         | bedrooms
| bthrms     | string         | bathrooms
| sqft       | string         | sqft
| price      | string         | price
| imgs       | array object   | [{img:'a/bcd123456.jpg', origImg:['P/C/BK.jpg'], imgHash:''}], 相同户型图可能来自多个condos.floorplan记录, origImg记录为数组
| unts       | array object   | [{ lvl:[1, 2, 3], unt:[1], fce:[N, E], fpId:'fpID1', origImg:'P/C/BK.jpg' }] 每条floorplan对应一个unit信息
| matchCnt   | number         | 经纪点击match的次数, 即使floorplan有多个户型图点了match也是针对整条floorplan记录
| matchUids  | array object   | eg:[ {uid:'', ts:'', propId:'', unt:''} ]点击match的经纪uid
| verified   | object         | eg: {status:'', uid:'', ts:''}
