<mxfile host="65bd71144e">
    <diagram id="C5RBs43oDa-KdzZeNtuy" name="Page-1">
        <mxGraphModel dx="807" dy="809" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-0"/>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0"/>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-3" target="UbhoMyGfK3VIYJcsGif8-3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-3" value="Send to Kafka:&lt;br&gt;{_id:'TRBW118118',&lt;br&gt;uaddr:'',&lt;br&gt;unt:'',&lt;br&gt;pics:[]&lt;br&gt;phoMt: ISODate&lt;b style=&quot;background-color: initial;&quot;&gt;(&lt;/b&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;&quot;2023-09-22T21:42:17.000+0000&quot;&lt;/span&gt;&lt;b style=&quot;background-color: initial;&quot;&gt;)};&lt;br&gt;&lt;/b&gt;add clsPhoSendMt to properties&lt;br&gt;&lt;font style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;record in imgClassificationCol&lt;/font&gt;&lt;font style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;:&amp;nbsp;&lt;/font&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqNewestTs,&lt;/span&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqOldestTs,&lt;/span&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqCount&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="64" y="190" width="362" height="140" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-6" target="20" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-6" target="WIyWlLk6GJQsqaUBKTNV-11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontColor=#FFFFFF;" parent="26" vertex="1" connectable="0">
                    <mxGeometry x="-0.0516" y="1" relative="1" as="geometry">
                        <mxPoint x="11" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-6" value="Kafka response phoMt=== properties.phoMt?" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="155" y="670" width="180" height="129" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-7" target="WIyWlLk6GJQsqaUBKTNV-6" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-7" value="Kafka response:&lt;br&gt;{_id:'TRBW118118',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;uaddr:'',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;unt:'',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;result:[]&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;phoMt: ISODate&lt;b style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;(&lt;/b&gt;&quot;2023-09-22T21:42:17.000+0000&quot;&lt;b style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;)}&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="145" y="490" width="200" height="100" as="geometry"/>
                </mxCell>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-11" value="Discard the result&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="185" y="857" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="WIyWlLk6GJQsqaUBKTNV-12" value="Add waterMark, result write in floorplanCol and&amp;nbsp;&lt;font style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;in imgClassificationCol:&lt;br&gt;&lt;/font&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqNewestTs,&lt;/span&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqOldestTs,&lt;/span&gt;&lt;span style=&quot;color: rgb(255, 255, 255); background-color: rgb(31, 31, 31);&quot;&gt;reqCount&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="404.5" y="505" width="231" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-0" value="properties：&lt;br&gt;_id&lt;br&gt;phoMt:&amp;nbsp;&lt;br&gt;clsPhoSendMt:&lt;br&gt;phoClsMt:" style="whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="660" y="190" width="150" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-3" target="WIyWlLk6GJQsqaUBKTNV-7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-3" value="FloorPlan classification" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="145" y="400" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=-0.02;entryY=0.42;entryDx=0;entryDy=0;entryPerimeter=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="WIyWlLk6GJQsqaUBKTNV-11" target="WIyWlLk6GJQsqaUBKTNV-7" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="85" y="877"/>
                            <mxPoint x="85" y="532"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-12" target="UbhoMyGfK3VIYJcsGif8-17" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-12" value="Kafka response (propertiesA)&lt;br&gt;{_id:'TRBW118118',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;uaddr:'',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;unt:'',&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;result:[]&lt;br style=&quot;border-color: var(--border-color);&quot;&gt;phoMt: ISODate&lt;b style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;(&lt;/b&gt;&quot;2023-09-22T21:42:17.000+0000&quot;&lt;b style=&quot;border-color: var(--border-color); background-color: initial;&quot;&gt;)}&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="32" y="1339" width="220" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="142" y="1725" as="sourcePoint"/>
                        <mxPoint x="142" y="1785" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-27" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UbhoMyGfK3VIYJcsGif8-26" vertex="1" connectable="0">
                    <mxGeometry x="-0.1833" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-34" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-17" target="UbhoMyGfK3VIYJcsGif8-31" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-35" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UbhoMyGfK3VIYJcsGif8-34" vertex="1" connectable="0">
                    <mxGeometry x="-0.06" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="5" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=24;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-17" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="345" y="1585" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-17" value="src==='mls' and sid==_id?" style="rhombus;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="27" y="1519" width="230" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-20" value="&amp;nbsp; add propertiesA.unt to floorplansB unts[]&amp;nbsp;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="40.5" y="2078" width="201" height="91" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-23" target="UbhoMyGfK3VIYJcsGif8-33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="470" y="1960"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="No" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="18" vertex="1" connectable="0">
                    <mxGeometry x="-0.3654" y="2" relative="1" as="geometry">
                        <mxPoint x="-3" y="-9" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-23" value="floorplansB.imgs.imgHash=== propertiesA.result.imgHash?" style="rhombus;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="27" y="1889" width="230" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-31" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="142" y="1889" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-30" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="UbhoMyGfK3VIYJcsGif8-29" vertex="1" connectable="0">
                    <mxGeometry x="-0.1833" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-31" value="&amp;nbsp;floorplans.uaddr===propertiesA.uaddr?" style="rhombus;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="27" y="1699" width="230" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-32" value="No" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;jettySize=auto;orthogonalLoop=1;fontSize=11;endArrow=block;endFill=0;endSize=8;strokeWidth=1;shadow=0;labelBackgroundColor=none;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="UbhoMyGfK3VIYJcsGif8-31" target="UbhoMyGfK3VIYJcsGif8-33" edge="1">
                    <mxGeometry x="-0.0065" y="-9" relative="1" as="geometry">
                        <mxPoint x="1" y="-18" as="offset"/>
                        <mxPoint x="348" y="1764" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="UbhoMyGfK3VIYJcsGif8-33" value="Create one new document in mongo&amp;nbsp;vow.floorplans and get info from vow.properities" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="379" y="1739" width="180" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="0" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;Write Kafka response to MongoDB&lt;/font&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="105" y="1210" width="515" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="&lt;font style=&quot;font-size: 24px;&quot;&gt;WatchPropAndSendToFPL Process Flowchart&lt;/font&gt;" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="140" y="110" width="515" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="10" style="edgeStyle=none;html=1;entryX=0.437;entryY=-0.006;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=11;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="4" target="UbhoMyGfK3VIYJcsGif8-33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="new imgHash in listA not in old listB" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;" parent="10" vertex="1" connectable="0">
                    <mxGeometry x="-0.5058" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" style="edgeStyle=none;html=1;fontSize=11;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="4" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="imgHash in listA and listB" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;" parent="12" vertex="1" connectable="0">
                    <mxGeometry x="-0.1666" y="1" relative="1" as="geometry">
                        <mxPoint x="4" y="-24" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;fontSize=11;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="4" target="7" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="imgHash not in listA but in listB" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=11;" parent="14" vertex="1" connectable="0">
                    <mxGeometry x="-0.1531" y="-2" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="4" value="compare new imgHash listA with old imgHash listB&amp;nbsp;" style="rhombus;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="359" y="1520" width="200" height="130" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="&lt;font style=&quot;font-size: 11px;&quot;&gt;Yes&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=24;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="266" y="1550" width="40" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="delete this img" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="373" y="1390" width="180" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="update using new result" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="680" y="1559.5" width="133" height="51" as="geometry"/>
                </mxCell>
                <mxCell id="16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="142" y="2078" as="targetPoint"/>
                        <mxPoint x="142" y="2018" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="16" vertex="1" connectable="0">
                    <mxGeometry x="-0.1833" y="3" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" style="edgeStyle=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="20" target="WIyWlLk6GJQsqaUBKTNV-12" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="Yes" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontFamily=Helvetica;fontColor=#FFFFFF;" parent="28" vertex="1" connectable="0">
                    <mxGeometry x="-0.0877" y="-3" relative="1" as="geometry">
                        <mxPoint x="11" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" value="No" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="600" y="734.5" as="sourcePoint"/>
                        <mxPoint x="660" y="734.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="20" value="local file exist?" style="rhombus;whiteSpace=wrap;html=1;shadow=0;fontFamily=Helvetica;fontSize=12;align=center;strokeWidth=1;spacing=6;spacingTop=-4;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="440" y="670" width="160" height="129" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="360" y="709" width="40" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="32" style="edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontFamily=Helvetica;fontColor=#FFFFFF;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="30" target="WIyWlLk6GJQsqaUBKTNV-12" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="725" y="540"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="30" value="Download the img" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;glass=0;strokeWidth=1;shadow=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
                    <mxGeometry x="660" y="699.5" width="130" height="70" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>