## db setup
download and dump collection census2016 to local db

## demographics image setup
clone rmlfs repo on same level of realmaster-appweb
go src
./bin/imgstocks.sh

## confirm on prop detail page
go to property page, check location demographics tab

## update demographics image or bbox
login to realmaster website as dev/admin
go to vip console
go to generateCensus Image
url query ?id=${censusId}
can update one or update all

## update new demographics image
if there is new images, after generate the new images,
./bin/imgstocks.sh compress
to get new compress file on rmlfs/imgstocks/census.tar.gz
push to server and uncompress to test

## files
front end component: /src/coffee4client/components/census/Demographics.vue
server api: /src/apps/80_sites/AppRM/stat/census.coffee #1.5/census/commu
helpers: /src/libapp/censusHelper.coffee  getDemographicSummary getCensusChartData