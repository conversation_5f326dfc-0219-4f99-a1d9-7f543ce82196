#libs list:

native:
"eventemitter2": "^5.0.1",
"handlebars": "^4.7.3",
"node-pre-gyp": "^0.14.0",
"react": "16.11.0",
"react-devtools": "^4.8.2",
"react-native": "0.62.0",
"react-native-calendar-events": "^1.7.3",
"react-native-camera": "^3.31.0",
"react-native-check-app-install": "^0.0.5",
"react-native-device-info": "^5.2.1",
"react-native-fbsdk": "^2.0.0",
"react-native-geolocation-service": "^3.1.0",
"react-native-gesture-handler": "^1.5.0",
"react-native-google-api-availability-bridge": "^1.3.0",
"react-native-localize": "^1.3.3",
"react-native-maps": "0.26.1",
"react-native-permissions": "^2.0.3",
"react-native-push-notification": "^5.1.0",
"react-native-screens": "^1.0.0-alpha.23",
"react-native-shake": "^3.4.0",
"react-native-share": "^3.7.0",
"react-native-splash-screen": "^3.2.0",
"react-native-vector-icons": "^7.0.0",
"react-native-webview": "^8.0.4",
"react-native-wechat-lib": "^1.1.21",
"react-navigation": "^4.0.10",
"react-navigation-stack": "^1.10.3",
"rn-fetch-blob": "^0.11.2"

web:
"apn": "^2.2.0",
"arangojs": "^5.8.0",
"async": "^2.6.3",
"aws-sdk": "^2.683.0",
"body-parser": "^1.19.0",
"bufferutil": "^4.0.1",
"chart.js": "^2.9.3",
"cheerio": "*",
"coffee-script": "^1.12.7",
"coffeekup": "^0.3.1",
"colors": "^1.4.0",
"cookie-parser": "^1.4.4",
"csv": "^5.3.1",
"csv-parser": "^1.12.1",
"csvtojson": "^1.1.12",
"date-utils": "*",
"dot": "^1.1.3",
"event-stream": "^4.0.1",
"faker": "^4.1.0",
"fast-csv": "^2.5.0",
"fast-stats": "0.0.3",
"formidable": "*",
"gc-profiler": "^1.4.1",
"geetest": "^4.0.0",
"geolib": "^2.0.24",
"gm": "^1.23.1",
"googleapis": "^51.0.0",
"halfstreamxml": "*",
"html-minifier": "^4.0.0",
"html-to-text": "^3.3.0",
"jade": "~1.6.0",
"js-yaml": "^3.13.1",
"jsdom": "^9.9.1",
"jsonwebtoken": "^8.5.1",
"jwt-simple": "^0.5.6",
"libphonenumber-js": "^1.7.29",
"line-by-line": "^0.1.6",
"lz-string": "^1.4.4",
"mandrill-api": "*",
"mime": "^2.4.4",
"moment-timezone": "^0.5.27",
"mongodb": "^3.5.6",
"mysql": "^2.17.1",
"nimble": "*",
"node-sass": "^4.14.1",
"node-uuid": "*",
"nodemailer": "^2.3.2",
"nodemailer-html-to-text": "^2.1.0",
"nodemailer-sendmail-transport": "^1.0.0",
"openid": "*",
"pg": "^8.0.3",
"prompt": "^1.0.0",
"redis-sessions": "^2.1.0",
"regression": "^1.4.0",
"request": "^2.88.0",
"serialize-javascript": "^1.9.1",
"simplify-geojson": "^1.0.3",
"simplify-js": "^1.2.4",
"sns-validator": "^0.3.4",
"socket.io": "^2.3.0",
"stripe": "^4.25.0",
"stylus": "^0.54.7",
"superagent": "^2.1.0",
"terser": "^4.6.3",
"terser-webpack-plugin": "^2.3.4",
"twilio": "^2.9.1",
"typescript": "^4.0.2",
"uglify-js": "^2.8.29",
"uglifycss": "0.0.29",
"uglifyjs": "^2.4.11",
"utf-8-validate": "^5.0.2",
"uuidv4": "^6.0.9",
"vee-validate": "^2.2.15",
"vue-i18n": "^4.6.0",
"vue-pull-refresh": "^0.2.7",
"vue-resource": "^1.5.1",
"vue-server-renderer": "^2.6.11",
"vuedraggable": "^2.23.2",
"wind-dom": "0.0.3",
"ya-csv": "*",
"yargs": "^15.3.1"
"JSONStream": "^1.3.5",
"babel-core": "^6.26.3",
"babel-loader": "^8.0.6",
"babel-plugin-transform-runtime": "^6.23.0",
"babel-polyfill": "^6.26.0",
"babel-preset-es2015": "^6.24.1",
"babel-runtime": "^6.26.0",
"coffee-loader": "^0.9.0",
"coffeescript": "^2.4.1",
"css-loader": "^1.0.1",
"eslint": "^3.2",
"file-loader": "^0.9.0",
"html-webpack-plugin": "^3.2.0",
"less": "^3.10.3",
"less-loader": "^4.1.0",
"parse5": "^2.2.3",
"postcss-loader": "^3.0.0",
"pug": "^2.0.4",
"pug-plain-loader": "^1.0.0",
"sass-loader": "^7.3.1",
"sharp": "^0.23.4",
"uglifyjs-webpack-plugin": "^2.2.0",
"url-loader": "^3.0.0",
"vue": "^2.6.11",
"vue-hot-reload-api": "^1.3.3",
"vue-html-loader": "^1.2.4",
"vue-loader": "^15.8.3",
"vue-progressbar": "^0.2.5",
"vue-style-loader": "^1.0.0",
"vue-template-compiler": "^2.6.11",
"vueify": "^8.7.0",
"webpack": "^4.41.4",
"webpack-cli": "^3.3.10",
"webpack-merge": "^4.2.2"


#known updates:
native:
react-navigation use new version
wx.share has new web share api


