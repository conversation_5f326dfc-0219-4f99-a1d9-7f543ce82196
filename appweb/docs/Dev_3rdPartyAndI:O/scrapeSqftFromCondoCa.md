Scrape sqft (+built yr?) from websites condos.ca, strata.ca 
infrustructure

- [ ] Batch server
    - [ ] when sqft aggregation has new address,  write to SqftToProcess，（change to write to both kafka and ）
    - [ ] driver
    - [ ] Mirror this topic(to process) to scrape server 
    - [ ]  存储结构
        - [ ] key： msg, timestamp.
- [ ] middle server
    - [ ] set up public key， remote key， ssl，
- [ ] scrape server
    - [ ] read data from kafka, get mls number to download
    - [ ] scrape data from condos.ca, using puppeteer,  
    - [ ] write downloaded data to kafka，
    - [ ] Mirror scrape server topic (like downloaded) Kafka to batch server
- [ ] Batch server
    - [ ] Read from Kafka, update sqft_aggregation table. Add Sqft, bltyr etc (update all properties?)， rmSqft., src from condo.ca 
    - [ ] 单独写到另外一个collection。save raw data
- [ ] Init process
    - [ ] Push all sqft address to Kafka 
