### general batch

# how to run all batch?
1. ./start.sh batch/[name].coffee [param]
2. read log

# follow this format in the header

### #what this batch do?
## options:
### -b \t-> shows xxxxbxxxx
### dry \t-> dry run
## examples:
###

    Description: 
    createDate:
    usage:          command --options
    Author:         name  |                            
    Run duration:   10min | 20min/20hr                 
    Run frequency:  once  | once/1day/7/30/365/depends
    Created:        time



# batch being runned on server
```bash
# 15 mins
0,15,30,45 * * * * /cfg/start.sh lib/batchBase.coffee batch/sendMail.coffee preload-model production >> /cfg/logs/sendMail.log 2>&1

# 30 mins
15,45 * * * * /cfg/start.sh lib/batchBase.coffee batch/wechat.coffee >> /cfg/logs/wechat.log 2>&1

# hourly
10 0-23 * * * /cfg/start.sh lib/batchBase.coffee mlsImport/dtaDownload.coffee -f hourly -s 1 >> /cfg/logs/dta.log 2>&1

# daily
30 2 * * * /cfg/start.sh lib/batchBase.coffee stats/prop_mw.coffee LastW >> /cfg/logs/statsLastW.log 2>&1
30 3 * * * /cfg/start.sh lib/batchBase.coffee stats/prop_mw.coffee LastM >> /cfg/logs/statsLastM.log 2>&1
1 3 * * * /cfg/start.sh lib/batchBase.coffee stats/prop_daily.coffee >> /cfg/logs/statsPropDaily.log 2>&1

0 9,21 * * * /cfg/start.sh lib/batchBase.coffee batch/dailyNotifyV2.coffee prod preload-model >> /cfg/logs/dailyNotify.log 2>&1
45 3 * * * /cfg/start.sh lib/batchBase.coffee batch/wepageClearCounter.coffee >> /cfg/logs/wepageClearCounter.log 2>&1

5 17 1-6,8-31 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1
5 17 7 * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd statM rmlog assignment exlisting daily production >> /cfg/logs/edmDaily.log 2>&1
1 18 * * * /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model property production >> /cfg/logs/edmDailySub.log 2>&1

# findschool
30 1 * * * /cfg/start.sh lib/batchBase.coffee batch/findschool/findschoolv4.coffee -f daily -s 1 >> /cfg/logs/findschoolv4.log 2>&1
# dump form input to Tao
1 7 * * * /cfg/start.sh lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' --frequency daily -s 1 >> /cfg/logs/formdump.log 2>&1
# statUname & addtag 【DEPRECATED!】 
# 10 1 * * * /cfg/built/statUname.sh -t calendar >> /cfg/logs/statUnameCalendar.log 2>&1
# findschool update bnds
30 2 * * * /cfg/start.sh lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -f >> /cfg/logs/processSchoolBndChange.log 2>&1

# weekly
1 1 * * 6 /cfg/start.sh lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw project forum stat rmlog noviewed assignment exlisting weekly production >> /cfg/logs/edmWeekly.log 2>&1
1 1 * * 1 /cfg/start.sh lib/batchBase.coffee batch/edmExpNotify.coffee preload-model 10 production >> /cfg/logs/edmExpWeekly.log 2>&1
0 2 * * 6 /cfg/start.sh lib/batchBase.coffee batch/user/getAgentWithListings.coffee >> /cfg/logs/laWeekly.log 2>&1
07 17 * * 3 /cfg/start.sh lib/batchBase.coffee batch/prop/reImportProp.coffee  >> /cfg/logs/reImportProp.log 2>&1 &
#10 3 * * 1 /cfg/start.sh lib/batchBase.coffee batch/removeDDFImage.coffee 365 /mnt/md0/mlsimgs/crea/ddf/img >> /cfg/logs/removeDDFImage.log 2>&1
#10 3 * * 2 /cfg/start.sh lib/batchBase.coffee batch/removeTREBImage.coffee 365 /mnt/md0/mlsimgs/treb/mls >> /cfg/logs/removeTrebImage.log 2>&1

# monthly
0 1 1 * * /cfg/start.sh lib/batchBase.coffee batch/user/getAgentRealChatStat.coffee 30 >> /cfg/logs/agntRealChatState-monthly.log 2>&1
0 1 2 * * /cfg/start.sh lib/batchBase.coffee batch/prop/addBuiltYear.coffee >> /cfg/logs/addBuiltYear-monthly.log 2>&1
# inreal monthly stat report
#1 0 3 * * /cfg/start.sh lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> > /cfg/logs/exportInRealGroupStats.log 2>&1 &
1 4 2 * * /cfg/sendInRealStatus.sh > /cfg/logs/exportInRealGroupStats.log 2>&1 &
sendInRealStatus.sh =
/home/<USER>/rmcfg/start.sh lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL>> /home/<USER>/rmcfg/logs/exportInRealGroupStats.log 2>&1 &

# daily backup
#10 3 * * * /user/bin/wpBackup.sh >> /user/logs/wpBackup.log
#30 3 * * * /user/bin/dbBackup.sh >> /user/logs/dbBackup.log
```



# New Config: batch being runned on server
```bash
# 5 mins
sh start.sh -t batch -n reDownloadRni_day3 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 3" -cron '*:0/5'

sh start.sh -t batch -n reDownloadRni_day7 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 7" -cron '*:0/5'

sh start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee" -cron '*:0/5'

# 5 mins
sh start.sh -t batch -n mongoTest -cmd "lib/batchBase.coffee batch/test/mongoTest.coffee" -cron '*:0/5'

# 15 mins
sh start.sh -t batch -n sendMail -cmd "lib/batchBase.coffee batch/sendMail.coffee preload-model production" -cron '*-*-* *:00,15,30,45:00'

# 30 mins
sh start.sh -t batch -n wechat -cmd "lib/batchBase.coffee batch/wechat.coffee" -cron '*-*-* *:15,45:00'

# hourly
sh start.sh -t batch -n dtaDownload -cmd "lib/batchBase.coffee mlsImport/dtaDownload.coffee -f hourly -s 1" -cron '*-*-* *:10:00'

# daily
sh start.sh -t batch -n checkRniTrebPropAndExpire -cmd "lib/batchBase.coffee batch/prop/checkRniTrebPropAndExpire.coffee" -cron '*-*-* 02:00:00'
sh start.sh -t batch -n statsLastW -cmd "lib/batchBase.coffee stats/prop_mw.coffee LastW" -cron '*-*-* 02:30:00'
sh start.sh -t batch -n statsLastM -cmd "lib/batchBase.coffee stats/prop_mw.coffee LastM" -cron '*-*-* 03:30:00'
sh start.sh -t batch -n statsPropDaily -cmd "lib/batchBase.coffee stats/prop_daily.coffee" -cron '*-*-* 03:01:00'

sh start.sh -t batch -n dailyNotify -cmd "lib/batchBase.coffee batch/dailyNotifyV2.coffee prod preload-model " -cron '*-*-* 09,21:00:00'
sh start.sh -t batch -n wepageClearCounter -cmd "lib/batchBase.coffee batch/wepageClearCounter.coffee" -cron '*-*-* 03:45:00'

sh start.sh -t batch -n edmDaily -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd assignment exlisting daily production" -cron '*-*-01..06,08..31 17:05:00'
sh start.sh -t batch -n edmDaily7 -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw projectrcmd statM rmlog assignment exlisting daily production" -cron '*-*-7 17:05:00'
sh start.sh -t batch -n edmDailySub -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model property production" -cron '*-*-* 18:01:00'

# findschool
sh start.sh -t batch -n findschoolv4. -cmd "lib/batchBase.coffee batch/findschool/findschoolv4.coffee -f daily -s 1" -cron '*-*-* 01:30:00'
# dump form input to Tao
sh start.sh -t batch -n formdump -cmd "lib/batchBase.coffee batch/form/formdump.coffee -e '<EMAIL>' -e '<EMAIL>' --frequency daily -s 1" -cron '*-*-* 07:01:00'

# findschool update bnds
sh start.sh -t batch -n processSchoolBndChange -cmd "lib/batchBase.coffee batch/prop/processSchoolBndChange.coffee preload-model -f" -cron '*-*-* 02:30:00'

# weekly
sh start.sh -t batch -n edmWeekly -cmd "lib/batchBase.coffee batch/edmNotify.coffee preload-model forumw project forum stat rmlog noviewed assignment exlisting weekly production" -cron 'Sat, 01:01:00'
sh start.sh -t batch -n edmExpWeekly -cmd "lib/batchBase.coffee batch/edmExpNotify.coffee preload-model 10 production" -cron 'Mon, 01:01:00'
sh start.sh -t batch -n laWeekly -cmd "lib/batchBase.coffee batch/user/getAgentWithListings.coffee" -cron 'Sat, 02:00:00'
sh start.sh -t batch -n reImportProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee" -cron 'Wed, 17:07:00'



# monthly
sh start.sh -t batch -n agntRealChatState-monthly -cmd "lib/batchBase.coffee batch/user/getAgentRealChatStat.coffee 30" -cron '*-*-01 01:00:00'
sh start.sh -t batch -n addBuiltYear-monthly -cmd "lib/batchBase.coffee batch/prop/addBuiltYear.coffee" -cron '*-*-02 01:00:00'
sh start.sh -t batch -n exportInRealGroupStats -cmd "lib/batchBase.coffee batch/export/inRealGroupStats.coffee -m `date -d "1 month ago" +'%Y-%m'` -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL> -e <EMAIL>" -cron '*-*-02 04:01:00'
```




start watch and import services by:
```
<!-- DEPRECATED! SQL PROPS_PART/OH/LA WATCH AND UPDATE @ca8-->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdatePostSql.coffee init force >> ./logs/watchPropSql.log 2>&1 &

<!-- PROPS_PART WATCH AND UPDATE @ca8-->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee routine force >> ./logs/watchPropMongo.log 2>&1 &

<!-- ES WATCH AND UPDATE @ca1/shf1-->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force >> ./logs/watchEs.log 2>&1 &

<!-- PUSH NOTIFY SAVED SEARCH AND WATCH @ca1 -->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndPushNotify.coffee preload-model init force > ./logs/watchPropPushNotify.log 2>&1 &

<!-- RNI -> PROPERTIES COL @ca8-->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow -h 0.5 routine force >> ./logs/watchAndImportToProperties.log 2>&1 &

<!-- CONDO.CA SQFT RESULTS @ca8-->
nohup ./start.sh lib/batchBase.coffee batch/prop/condosSqft.coffee >> logs/condoSqft.log 2>&1 &

<!-- CREA PHOTO @ca8-->
nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee crea force >> logs/creaPhoto.log 2>&1 &

<!-- DEPRECATED! -->
nohup ./start.sh lib/batchBase.coffee mlsImport/photoDownload.coffee treb force >> logs/trebPhoto.log 2>&1 &

<!-- RETS PHOTO @ca8-->
nohup ./start.sh lib/batchBase.coffee mlsImport/retsPhotoDownloadV2.coffee treb DXXXXX '2XXXX' force >> logs/trebPhotoV2frx.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/retsPhotoDownloadV2.coffee treb D23XXX 'D2XXX' force >> logs/trebPhotoV2ywa.log 2>&1 &

<!-- PROP SRCS DOWNLOAD @ca8-->
nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownloadV2.coffee idx DXXXXu '9XXXX3' force >> logs/trebIDX.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/trebDownloadV2.coffee evow EXXXXu '7XXXX4' force >> logs/trebEVOW.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/creaDownload.coffee BfXXXXXxg WDXXXXXh force >> logs/creaDownload.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/bcreDownload.coffee RXXXXG HuXXXXop force >> logs/bcDownload.log 2>&1 &
nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownload.coffee -t 3XXXXXXTOKEN force >> logs/rahbDownload.log 2>&1 &

nohup ./start.sh lib/batchBase.coffee mlsImport/rahbDownloadOfficeAndMember.coffee -t 3XXXXTOKEN force >> logs/rahbOfficeDownload.log 2>&1 &

<!-- soldPrice -->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndCompareSoldPrice.coffee preload-model routine > ./logs/watchSoldProps.log 2>&1 &

<!-- get result from qq server, make sure qq has right result -->
nohup ./start.sh lib/batchBase.coffee batch/prop/condosSqft.coffee > ./logs/condoSqft.log 2>&1 &

<!-- DEPRECATED!, after change to ES in dailyNotify, this no longer need to run -->
nohup ./start.sh lib/batchBase.coffee batch/prop/watchPropAndUpdateMongo.coffee routine > ./logs/watchPropMongo.log 2>&1 &
```
