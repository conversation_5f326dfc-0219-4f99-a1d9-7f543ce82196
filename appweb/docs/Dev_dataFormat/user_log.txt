user_log 数据类型


tp : rmlisting/property 房源浏览历史
{
    _id : 自动生成
    id : mls房源_id/rm房源id
    tp : 数据类型
    uid : 用户id
    cnt : 浏览房源次数
    exp : 数据过期时间
    mt : 最后一次浏览房源时间
    tss : 浏览房源时间列表
}

tp : propChg 房源状态修改匹配上的条件
{
    _id : 自动生成
    date : 日期(yyyymmdd) tp相同一个用户一天只生成一条数据，若有新增则在src中继续添加
    id : 状态改变的房源id
    uid : 用户id
    hasNewChange : 是否有新增
    src : 匹配上的条件
      [
        {
          k : 匹配的类型(Saved Home/Community/Recently viewed/Showing/Location/Location/Building)
          v : [
              {
                  nm : 匹配条件的名称
              }
          ]
        },
        {
          k : 匹配的类型(Saved Search)
          v : [
              {
                  nm : 匹配条件的名称
                  clnt : 被转发的客户_id(客户信息在data.crm表中)
                  cNm : 被转发的客户名字(保存在data.crm表中)
              }
          ]
        }
      ],
    status : 房源变动状态 (sale/sold/chgd)
    tp : 数据类型
    ts : 创建时间
}
