short url 数据格式

type : 'shareProp' 分享房源时保存的数据
{
  _id : 十位生成的NanoId
  type : 数据类型
  data : {
    share : 1,
    id : 房源id，多个时以,分隔
    tp : 区别单个房源或者列表(listing/listingList),
    wDl : 1,
    aid : 分享用户uid
    lang : en,
    to : A
  },
  uid : 分享用户uid
  channel : 分享方式
  ts : ISODate(2024-03-13T10:53:14.107+0000),
  _exp30d : ISODate(2024-06-11T10:53:38.774+0000),
  _mt : ISODate(2024-03-13T10:53:38.784+0000),
  统计：统计点击链接的人来源和权限
    app : {
        realtor : 点击链接的人的角色
    },
    clicks : 总点击次数
}