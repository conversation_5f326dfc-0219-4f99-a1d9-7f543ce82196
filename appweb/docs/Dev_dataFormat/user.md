## chomeca.user 数据结构
```
{
    "_id" : 系统生成
    "eml" : 邮箱
    "src" : "iphone:4.0.0",
    "id" : "RM_42",
    "wxuid" : 微信登录id,
    "balance" : NumberInt(13),
    "ip" : 登录ip
    "itr" : introduction介绍
    "lts" : 上次登录时间
    "locale" : 界面语言
    "roles" : 权限相关的role[
        "realtor",
        "_create_proj",
        "_provAdmin",
        "_admin",
        "_cpm",
        "vip_plus",
        "_dev"
    ],
    "devId" : native获取的device id
    "cities" : 首页城市[
        {
            "city" : "Mississauga",
            "prov" : "Ontario",
            "lat" : 43.6029920781437,
            "lng" : -79.6559697259811
        },
    ],
    "rid" : realtor id
    "mbl" : mobile number
    "cpny" : company公司名称
    "addr" : 地址
    "tel" : 电话
    "fax" : 传真
    "sas" : 服务地区[
        {
            "city" : "Toronto",
            "prov" : "ON"
        },
    ],
    "lid" : "1231313",
    "pstn" : "some pstn",
    "city" : {
        "city" : "Aurora",
        "prov" : "ON",
        "lat" : 43.************,
        "lng" : -79.4793950408097
    },
    "avt" : 头像 "G/U/A.jpg?v=271711",
    "actpts" : NumberInt(136),
    "rmb" : NumberInt(650),
    "wsc" : NumberInt(0),
    "cip" : china ip,
    "lic" : "lic123aaaa",
    "cpny_en" : 公司名en
    "cpny_zh" : 公司名zh
    "nm_en" : 显示名称en
    "nm_zh" : 显示名称zh
    "ddf_rid" : "DDF2030599",
    "qrcd" : qr code，可扫码
    "vip_mt" : ISODate("2020-11-30T22:21:23.033+0000"),
    "appVer" : app的version
    "noFltr" : true,
    "fornm" : 论坛名称
    "mt" : ISODate("2024-05-14T20:47:57.961+0000"),
    "market" : {
        "cpny" : "some company",
        "eml" : "<EMAIL>",
        "fnm" : "aaa77981",
        "itr" : "Cras a mi eu lorem volutpat vestibulum non vitae turpis. Pellentesque vitae imperdiet arcu, nec suscipit nibh. Nulla facilisi. Etiam suscipit diam congue mauris aliquam tempor. Morbi a justo erat. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.",
        "mbl" : NumberInt(22222),
        "mt" : ISODate("2024-04-01T19:54:22.733+0000"),
        "tl" : " some company "
    },
    "forBlk" : true,
    "58" : {
        "cpny" : "some company",
        "eml" : "<EMAIL>",
        "fnm" : "aaa77981",
        "itr" : "Cras a mi eu lorem volutpat vestibulum non vitae turpis. Pellentesque vitae imperdiet arcu, nec suscipit nibh. Nulla facilisi. Etiam suscipit diam congue mauris aliquam tempor. Morbi a justo erat. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.",
        "mbl" : NumberInt(22222),
        "mt" : ISODate("2022-04-04T15:45:06.757+0000"),
        "tl" : " some company "
    },
    "_mt" : ISODate("2024-05-14T20:47:57.977+0000"),
    "stars" : 4.5,
    "hasWechat" : NumberInt(0),
    "appmode" : "mls",
    "splang" : [
        "Mandarin",
        "English",
        "Cantonese"
    ],
    "dGids" : [
        ObjectId("5d38e416b6cd1729d2194b8d")
    ],
    "defaultHomeTab" : "new",
    "switchUsers" : [
      "<EMAIL>"
    ],
    "ts" : ISODate("2016-09-13T00:22:24.000+0000"),
    "pn" : push notify的token
    "cpny_pstn" : 公司的职位
    "fburl" : facebook 链接
    "mblalt" : NumberInt(31313),
    "nm" : Nick name
    "qq" : QQ号
    "sex" : "Male", 性别
    "sgn" : "whatsup",
    "twturl" : "twitter 链接",
    "web" : "个人web url",
    "wurl" : "weibo url 链接",
    "wx" : "wechat_id",
    "wxgrp" : "订阅号id",
    "wxgrpnm" : "订阅号名称"
    uuid: 后端分配的uuid
    b4luuid: 注册登录之前的uuid，在cookie里
}
```
## uuid 数据结构
类似于pn,需要uuid相关表格
{
  _id: mongoId
  uuid: 后端分配的id，
  devid: 当时使用的devid
}
# 未登录可以进行的操作：查看房源