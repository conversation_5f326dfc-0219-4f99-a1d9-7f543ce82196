### mongodb setup for RealMaster projects
1. install mongodb [here](https://www.mongodb.com/docs/manual/tutorial/install-mongodb-on-os-x/)
2. create users [link](https://www.mongodb.com/docs/manual/reference/command/createUser/#mongodb-dbcommand-dbcmd.createUser)
3. restore template data from dump
   ```
   mongodump -c properties -d listing -h ca12 --port 27017 -u d1 --authenticationDatabase admin --gzip --ssl --tlsInsecure  --sslCAFile '/etc/mongo/ca.crt'  -q='{"spcts":{"$gte":{"$date":"2022-03-11T00:00:00.000Z"}}}'
   
   mongorestore -u [d1] --db [listingTest] --gzip --port 27017 --host 127.0.0.1 --authenticationDatabase admin -c properties properties.bson
   ```
4. change mongodb local to replica set [link](https://gist.github.com/davisford/bb37079900888c44d2bbcb2c52a5d6e8)

# test user create
```
use admin

db.createUser(
  {
    user: "dbadmin",
    pwd: "dbadmin",
    roles: [ { role: "userAdminAnyDatabase", db: "admin" } ]
  }
)


db.createUser({
  user: "d1t",
  pwd:"d1t",
  roles:
    [
      { role: "readWrite", db: "dataTest" },
      { role: "readWrite", db: "listingTest" },
      { role: "dbAdmin", db: "dataTest" },
      { role: "dbAdmin", db: "listingTest" }
    ]
})

db.createUser({
  user:"rnit",
  pwd:"rnit",
  roles:
    [
      { role: "readWrite", db: "rniTest" },
      { role: "dbAdmin", db: "rniTest" },
    ]
})

db.createUser({
  user:"d1",
  pwd:"d1",
  roles:
    [
      { role: "readWrite", db: "listing" },
      { role: "readWrite", db: "data" },
    ]
})
```