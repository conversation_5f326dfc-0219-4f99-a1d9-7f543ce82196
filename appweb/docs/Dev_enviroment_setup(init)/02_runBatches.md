
* install Elastic Search (optional) required if useSearchEngine: 'ES'
* install PgSql (optional)
* install node & nvm
* install python3
* install coffeescript
* install redis, then redis-server

1. git clone cfglocal (d1 branch)
add to /etc/hosts
```
127.0.0.1       localhost app.test www.test
```
2. check if `config.coffee` contains `srcPath`. If not, add `srcPath: <%this.appPath%>/src`
3. make sure cfg is correct for mongodb and ES pwd,
    - set cfg.useSearchEngine: `'Mongo'` if no ES
    - change mongodb configuration (port, db, user, pass)in `base.yml` for listing, data and rni databases
    - create a ES user before setting it as the SearchEngine
    - check if the path of `http_cra.cert` is correct
```
npm install
coffee setup.coffee
./start.sh
``` 
4. change `production` in `api_config` to false
5. add new site
```
./start.sh lib/batchBase.coffee batch/webSetting/addNewSite.coffee
db.properties.aggregate([
    { $group : { _id : "$rltr" } },
    {$out: 'prop_rltrs'}
])
```
6. start watch and import to properties. see docs/Dev_batch/general.md to run watch ES/watchPropAndUpdateMongo accordingly
7. restore prov_city_cmty from vow(listing) to local/server
7. restore cmtyFromProperties from vow(listing) to local/server
8. test server need to run setupForTestDb.coffee
9. dump and restore partial db from test server



### TO DUMP DB FROM TEST SERVER:

mongodump -c prov_city_cmty -d listing2 -h 127.0.0.1 --port 27018  -u d1 --authenticationDatabase admin --gzip

mongodump -c properties -d listing2 -h 127.0.0.1 --port 27018  -u d1 --authenticationDatabase admin --gzip -q='{"spcts":{"$gte":{"$date":"2023-03-11T00:00:00.000Z"}}}'


### TO RESTORE DB ON LOCAL:
mongorestore -u [d1] --db [listingTest] --gzip --port 27017 --host 127.0.0.1 --authenticationDatabase admin -c [properties] [properties].bson


### TO RESTORE IMGS ON LOCAL/SERVER:
* git clone rmlfs to rmcfg level
* unzip imgs for census21 & cmtys
```
tar -xf /rmlfs/imgstocks/census2021.tar.gz -C /realmaster-appweb/src/webroot/public/img/imgstocks/census
tar -xf /rmlfs/imgstocks/cmtys.tar.gz -C /realmaster-appweb/src
```