2023.11.8 测试文档

项目需求：

1. 楼花转让列表头像对普通用户不可见，对经济角色可见

2. 楼花转让详情界面增加hide按钮，对于拥有assignAdmin权限的人可见，作用：点击hide按钮后该房源会添加private字段作为标记，如果标记为true则该房源会在列表和地图中隐藏，非assignAdmin权限的用户无法看到，无论该房源被发布者撤销或者重新发布该字段的状态不会改变；assignAdmin权限的用户可以在列表和地图中看见，并且有'Hide to the public'标识文字

3. 真楼花的sold详细页面显示sold标识，与mls房源的sold详细界面样式保持一致

4. 楼花转让的报名表针对普通用户（没有跟随经济flwngRm或者flwng）发送指定邮箱，否则保持现有发送事件不变。详情可参见libapp/contactRealtorAssignment.coffee

针对需求一测试用例:

| 名称     | 描述                                                         | 结果 |
| -------- | ------------------------------------------------------------ | ---- |
| 测试项   | 楼花转让列表——针对不同角色登录用户经济头像显示功能测试       |      |
| 测试环境 | 手机app，7号测试服                                           |      |
| 输入标准 | 1. 使用账号********************登录，该账号是普通用户角色<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页进行浏览<br />3. 在楼花转让类型下可点击筛选条件进行列表筛选 |      |
| 输出标准 | 1. 进入楼花转让列表页普通用户在每个房源卡片的右下角不可见经济头像<br />2. 输入筛选条件后的列表也不可见经济头像 |      |
| 输入标准 | 1. 使用账号****************登录，该账号是经济角色<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页进行浏览<br />3. 在楼花转让类型下可点击筛选条件进行列表筛选 |      |
| 输出标准 | 1. 进入楼花转让列表页经济角色在每个房源卡片的右下角可见经济头像<br />2. 输入筛选条件后的列表也可见经济头像 |      |
| 输入标准 | 1. 不登录情况下，进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页进行浏览<br />2. 在楼花转让类型下可点击筛选条件进行列表筛选 |      |
| 输出标准 | 1. 进入楼花转让列表页在每个房源卡片的右下角不可见经济头像<br />2. 输入筛选条件后的列表也不可见经济头像 |      |

针对需求二测试用例:

| 名称     | 描述                                                         | 结果 |
| -------- | ------------------------------------------------------------ | ---- |
| 测试项   | 楼花转让房源隐藏——隐藏后的楼花转让房源对非楼花团队不可见     |      |
| 测试环境 | 手机app，7号测试服                                           |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 随机选择一个楼花转让房源点击进入详情页<br />4. 在 Listing Verification里点击hide键进行隐藏该房源，记住该房源的价格或者id等便于查找该房源的信息<br />5. 重新进入楼花转让列表页，在列表页找到隐藏的房源<br />6.在首页点击顶部搜索按钮，输入该房源id或其他信息进行查找并进入详情页<br />7.在地图中查找该房源<br />8. 可重复步骤3.4隐藏多个房源进行测试 |      |
| 输出标准 | 1. 点击hide按钮后提示success，hide按钮文字变成Unhide<br />2. 隐藏房源后在列表页看到该房源卡片出现 'Hide to the pubilc'<br />3. 不论何准方式搜索出来的房源进入详情页看到的按钮状态都是Unhide |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色不包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 在列表页查找隐藏的房源 <br />4. 在首页点击顶部搜索按钮，输入该房源id或其他信息进行查找<br />5. 在地图中查找该房源 |      |
| 输出标准 | 1. 楼花转让列表页查找不到隐藏的房源<br />2. 顶部搜索按钮进行搜索，找不到该隐藏的房源<br />3. 地图查看不到隐藏的房源 |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，在首页推荐中选择楼花转让的房源进入详情页<br />4. 在Listing Verification里点击hide键进行隐藏该房源，记住该房源的价格或者id等便于查找该房源的信息<br />5. 重新进入楼花转让列表页，在列表页找到隐藏的房源 |      |
| 输出标准 | 1. 点击hide按钮后提示success，hide按钮文字变成Unhide<br />2. 隐藏房源后在列表页看到该房源卡片出现 'Hide to the pubilc' |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色不包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，查看推荐<br />3. 在列表页查找隐藏的房源 |      |
| 输出标准 | 1. 在市场的首页推荐中查看不到隐藏的房源<br />2. 楼花转让列表页查找不到隐藏的房源 |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，在被隐藏的房源中选择一个房源进入详情页<br />3. 在Listing Verification里点击Unhide键取消隐藏该房源，记住该房源的价格或者id等便于查找该房源的信息<br />4. 重新进入楼花转让列表页，在列表页找到隐藏的房源<br />5. 可重复步骤2.3 |      |
| 输出标准 | 1. 点击Unhide键提示success<br />2. 在列表页该房源的'Hide to the pubilc'文字不出现 |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色不包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 在列表页查找取消隐藏的房源 <br />4. 在首页点击顶部搜索按钮，输入该房源id或其他信息进行查找<br />5. 在地图中查找该房源 |      |
| 输出标准 | 1. 楼花转让列表页可查找到取消隐藏的房源<br />2. 顶部搜索按钮进行搜索，可找到该隐藏的房源<br />3. 地图可查看到取消隐藏的房源 |      |
| 输入标准 | 1. 使用账号****************登录，该账号角色包含assignAdmin楼花转让团队<br />2. 进入市场Marketplace首页，点击非楼花转让的房源列表，进入房源详情页<br />|      |
| 输出标准 | 1. hide按钮不可见 |      |

针对需求三测试用例:

| 名称     | 描述                                                         | 结果 |
| -------- | ------------------------------------------------------------ | ---- |
| 测试项   | 楼花转让房源sold详情——显示sold标签同mls sold房源详情样式保持一致 |      |
| 测试环境 | 手机app，7号测试服                                           |      |
| 输入标准 | 1. 使用随意角色的账号登录<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 点击更多选择sold，Assignment进行搜索<br />4. 点击搜索出来的房源卡片进入详情页<br />5. 切换语言在再次查看 |      |
| 输出标准 | 1. 详情页出现红色sold标签，在划线的价格下方有sold.assignment |      |

针对需求四测试用例:

| 名称     | 描述                                                         | 结果                       |
| -------- | ------------------------------------------------------------ | -------------------------- |
| 测试项   | 楼花转让报名表——针对不同情况显示不同报名表以及报名表发送     |                            |
| 测试环境 | 手机app，7号测试服                                           |                            |
| 输入标准 | 1. 使用********************账号登录，该账号为没有任何跟随经济的普通用户<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 选择楼花转让（注意不要选择真楼花），点击获取更多信息<br />4. 输入报名信息，点击提交 |                            |
| 输出标准 | 1. 点击获取更多信息后出现报名表格<br />2. 提交后界面显示报名成功，****************邮箱会收到两封邮件,一封是报名的邮件，一封是告知有人通过什么形式报名 |                |
| 输入标准 | 1. 使用****************账号登录，该账号为经济角色<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 选择楼花转让（注意不要选择真楼花），点击获取更多信息 |                            |
| 输出标准 | 1. 显示经济联系方式的卡片                                    |                            |
| 输入标准 | 1. 使用****************账号登录，该账号为关联外部经济的普通用户<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 选择楼花转让（注意不要选择真楼花），点击获取更多信息 |                            |
| 输出标准 | 1. 显示关联的经济联系方式的卡片                              |                            |
| 输入标准 | 1. 使用********************账号登录，该账号为关联房大师经济的普通用户<br />2. 进入市场Marketplace首页，点击楼花转让，进入楼花转让列表页<br />3. 选择楼花转让（注意不要选择真楼花），点击获取更多信息<br />4. 输入报名信息点击提交 |                            |
| 输出标准 | 1. 点击获取更多信息后出现报名表格+owner联系方式<br />2.提交后界面显示报名成功，****************邮箱会收到一封邮件，告知报名走了api | |
| 输入标准 | 1. 随意使用任何角色账号登录<br />2. 进入二手房首页，点击真楼花房源，进入真楼花详情页<br />3. 点击获取更多信息<br />4. 输入报名信息点击提交 |                            |
| 输出标准 | 1. 点击获取更多信息后出现报名表格<br />2. 提交后界面显示报名成功，****************邮箱会收到会收到两封邮件,一封是报名的邮件，一封是告知通过指定邮箱形式报名 |                |

