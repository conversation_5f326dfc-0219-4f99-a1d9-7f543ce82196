# copy data
1. copy data /mnt/files/appd3/dump/d2listing 

2. mongoimport --mode upsert 导入geo cache 差分json数据，
mongoimport --port 27018 --authenticationDatabase 'admin' --db=d2listing --username=d2 --password=d2  --collection=geo_cache  --mode=upsert --file=./geo_cache.json

3. import all collection,boundary,school, census restore 要drop原来的。
mongorestore --port 27018 --drop --authenticationDatabase admin -u d2 -p d2 --gzip -d listing d2listing

====================================================================================
以下还未执行
mongorestore --port 27018  --drop --authenticationDatabase admin -u d2 -p d2 --gzip --nsInclude d2listing.*

4.#add index for future. Not used when tagging.

db.getCollection('properties').createIndex({rmmt:-1})

#index for boundary query
db.getCollection('properties').createIndex({'bndCmty._id':1})
db.getCollection('properties').createIndex({'cs16':1})

#may need for future query
db.getCollection('properties').createIndex({'trnst':1})
db.getCollection('properties').createIndex({'provErr':1})

5. fix geo location, reimport treb and ddf records. refer to fixGeoLocation.txt

=============
2020-06-23 更新

添加tag前需要fix boundary 的id和boudary bbox
./start.sh batch/correctData/addBboxToBoundary.coffee

修复uaddr
copy dump的geo cache 文件到server 并restore
/home/<USER>/dump0623/dump/listing geo_cache.bson.gz  geo_cache.metadata.json.gz
修复uaddr
nohup ./start.sh batch/correctData/fixPropertyUaddr.coffee  > ./logs/fixPropertyUaddr.log &

#
#add prop tag
# params:property, census, transit, school,walkscore,  init, routine, dryrun, drycount useCheckPoint force
# init: all properties or from checkPoint (if with useCheckPoint param)

# about 10+H
nohup ./start.sh batch/tag/addTagToProperty.coffee init school prop census census2016 transit boundary force > ./logs/proptag.log &
nohup ./start.sh batch/tag/addTagToProperty.coffee routine school prop census census2016 transit boundary > ./logs/proptagRoutine.log &

# add tag to boundary collection avg. school,transit
nohup ./start.sh batch/tag/addTagToBoundary.coffee boundary tracking > ./logs/boundarytag.log &
# add tag to boundary census tag
nohup ./start.sh batch/tag/addTagToBoundary.coffee census tracking > ./logs/boundarytag.log &


addtag 如果加到daily task之后，停止importSchoolAndCensus.coffee 的daily job


=======参考命令========
mongodump --port 27018 --db d2listing -u d2 -p d2 -c school  --gzip --authenticationDatabase 'admin'

mongodump --port 27018 --db d2listing -u d2 -p d2 -c geo_cache  --gzip --authenticationDatabase 'admin' -q '{"ts":{"$gte":{"$date":"2020-03-13T00:51:05.794Z"}}}'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c boundary  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c census2016  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c census2016Keys  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c census2016ReversKeys  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c census2016Notes  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c transit_stop  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c transit_route  --gzip --authenticationDatabase 'admin'
mongodump --port 27018 --db d2listing -u d2 -p d2 -c census  --gzip --authenticationDatabase 'admin'


mongoexport --port 27018 --db d2listing --username d2 --password d2 --authenticationDatabase 'admin' -c geo_cache   -q ' { "ts": { "$gte": { "$date" : "2020-03-13T00:51:05.794Z" } } }' --out ./geo_cache.json

mongoimport --port 27018 --authenticationDatabase 'admin' --db d2listing -u d2 -p d2  -c geo_cache --file geo_cache.json --upsert

mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c census2016ReversKeys census2016ReversKeys.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c census2016Keys census2016Keys.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c census2016Notes census2016Notes.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c census2016 census2016.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c transit_stop transit_stop.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c transit_route transit_route.bson.gz --gzip
mongorestore --port 27018 --authenticationDatabase admin -u d2 -p d2 --db d2listing -c boundary boundary.bson.gz --gzip

# add city to stop and city
./start.sh batch/boundary/addCity2Col.coffee force transitStop 
./start.sh batch/boundary/addCity2Col.coffee force cmty
#

1.scp -P 1023 -r appd3@*************:/mnt/files/appd3/dump/d2listing ~
5. #add bnds to school collection
./start.sh batch/boundary/bnToGeojson.coffee school，school错误稍后手动修改。
./start.sh batch/boundary/bnToGeojson.coffee census 



db.getCollection('school').createIndex({'bnds.features.geometry':'2dsphere'})
db.getCollection('boundary').update({src:'treb'},{$set:{tp:'cmty'}},{multi:true})
db.getCollection('boundary').createIndex({"bnds.features.geometry" : "2dsphere",tp:1})
db.getCollection('transit_route').createIndex({"bnds.features.geometry" : "2dsphere"})

db.getCollection('census').createIndex({"bnds.features.geometry" : "2dsphere"})

=====================================================
