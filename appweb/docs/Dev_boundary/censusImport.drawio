<mxfile host="65bd71144e">
    <diagram id="1Ij_MdHP6DMkF1okit-R" name="census import">
        <mxGraphModel dx="844" dy="1586" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="11" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="gmlbounday.geojson&lt;br&gt;census boundary file" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="20" y="140" width="150" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="3" target="25">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="2016_census_data.csv&lt;br&gt;census data file" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="25" y="730" width="160" height="80" as="geometry"/>
                </mxCell>
                <UserObject label="&lt;span style=&quot;color: rgb(51 , 51 , 51) ; font-family: &amp;#34;noto sans&amp;#34; , sans-serif ; font-size: 16px ; text-align: left ; background-color: rgb(245 , 245 , 245)&quot;&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;br&gt;&lt;/span&gt;" link="https://www12.statcan.gc.ca/census-recensement/2016/dp-pd/prof/details/page_Download-Telecharger.cfm?Lang=E&amp;Tab=1&amp;Geo1=PR&amp;Code1=01&amp;Geo2=PR&amp;Code2=01&amp;SearchText=Canada&amp;SearchType=Begins&amp;SearchPR=01&amp;B1=All&amp;TABID=1&amp;type=0" id="4">
                    <mxCell style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
                        <mxGeometry x="350" y="-65" width="20" height="190" as="geometry"/>
                    </mxCell>
                </UserObject>
                <mxCell id="16" value="" style="edgeStyle=none;html=1;exitX=1.023;exitY=0.371;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="N9zlu-W4jLNovWTdL9ni-4" target="15">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="170" y="443.6138613861385" as="sourcePoint"/>
                        <mxPoint x="280" y="441.4356435643565" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="N9zlu-W4jLNovWTdL9ni-4" value="2016_census_notes&lt;br&gt;" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" parent="1" vertex="1">
                    <mxGeometry x="35" y="395" width="135" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="N9zlu-W4jLNovWTdL9ni-7" value="&lt;span&gt;2016 年census的数据文件是allen给的， 对比后确定数据源应该来自&lt;/span&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="47.5" y="-95" width="400" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="N9zlu-W4jLNovWTdL9ni-8" value="2021 年同样的数据title下数据结构，统计指标从2247个变成了368个&amp;nbsp;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=10;" parent="1" vertex="1">
                    <mxGeometry x="35" y="30" width="390" height="40" as="geometry"/>
                </mxCell>
                <UserObject label="Census metropolitan areas (CMAs), tracted census agglomerations (CAs) and census tracts (CTs)" link="https://www12.statcan.gc.ca/census-recensement/2016/dp-pd/prof/details/page_Download-Telecharger.cfm?Lang=E&amp;Tab=1&amp;Geo1=PR&amp;Code1=01&amp;Geo2=PR&amp;Code2=01&amp;SearchText=Canada&amp;SearchType=Begins&amp;SearchPR=01&amp;B1=All&amp;TABID=1&amp;type=0" id="7">
                    <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                        <mxGeometry x="65" y="-65" width="365" height="30" as="geometry"/>
                    </mxCell>
                </UserObject>
                <UserObject label="boundary file 来自 boundary census tracts. download shp file, transter to geojson" link="https://www12.statcan.gc.ca/census-recensement/2011/geo/bound-limit/bound-limit-2016-eng.cfm" id="9">
                    <mxCell style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                        <mxGeometry x="65" y="-30" width="320" height="30" as="geometry"/>
                    </mxCell>
                </UserObject>
                <mxCell id="13" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="10" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="importBoundary.coffee census&lt;br&gt;&lt;br&gt;import boundary first" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="265" y="150" width="165" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="census2016" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="480" y="140" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="census2016Notes&lt;br&gt;for dislay" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="460" y="370" width="130" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="H9jIWV_buPSpjajk8fQu-37" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=0;entryY=0;entryDx=0;entryDy=52.5;entryPerimeter=0;" edge="1" parent="1" source="15" target="14">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="processNotes.coffee" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="260" y="390" width="145" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="17" target="18">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;span&gt;2016_census_keys&lt;/span&gt;" style="shape=document;whiteSpace=wrap;html=1;boundedLbl=1;" vertex="1" parent="1">
                    <mxGeometry x="35" y="500" width="125" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=none;html=1;" edge="1" parent="1" source="18" target="20">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="18" target="22">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="650"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="18" value="&lt;span&gt;processKeys.coffee&lt;/span&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="280" y="505" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="conm82Grl63VeQ947jfC-31" value="" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="20" target="22">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="census2016Keys" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="510" y="495" width="140" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=orthogonalEdgeStyle;html=1;" edge="1" parent="1" source="22" target="25">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="340" y="670"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;&lt;div style=&quot;font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;census2016ReversKeys&lt;/div&gt;&lt;/div&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="1">
                    <mxGeometry x="500" y="610" width="160" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="27" style="edgeStyle=orthogonalEdgeStyle;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="25" target="12">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="720" y="770"/>
                            <mxPoint x="720" y="180"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="25" value="processCensus.coffee" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="280" y="740" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="document path&amp;nbsp; &amp;nbsp;docs/dev_boundary/import2016census.txt" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="47.5" y="70" width="330" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="H9jIWV_buPSpjajk8fQu-31" value="&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;/boundary?col=census2016Error&lt;/div&gt;&lt;div style=&quot;color: rgb(212 , 212 , 212) ; background-color: rgb(30 , 30 , 30) ; font-family: &amp;#34;menlo&amp;#34; , &amp;#34;monaco&amp;#34; , &amp;#34;courier new&amp;#34; , monospace ; line-height: 18px&quot;&gt;correct error bnds in web boundary manage&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" vertex="1" parent="1">
                    <mxGeometry x="180" y="260" width="280" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="H9jIWV_buPSpjajk8fQu-34" value="" style="endArrow=classic;startArrow=classic;html=1;edgeStyle=orthogonalEdgeStyle;" edge="1" parent="1" target="12">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="460" y="290" as="sourcePoint"/>
                        <mxPoint x="500" y="260" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="460" y="290"/>
                            <mxPoint x="550" y="290"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>