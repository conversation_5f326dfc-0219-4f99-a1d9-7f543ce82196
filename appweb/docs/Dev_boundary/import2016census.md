
# where to get the source file

## census file
download from Statistics Canada->Census->Census of Population -> census data ->
link for 2016
https://www12.statcan.gc.ca/census-recensement/2016/dp-pd/prof/details/page_Download-Telecharger.cfm?Lang=E&Tab=1&Geo1=PR&Code1=01&Geo2=PR&Code2=01&SearchText=Canada&SearchType=Begins&SearchPR=01&B1=All&TABID=1&type=0


link for 2021
https://www12.statcan.gc.ca/census-recensement/2021/dp-pd/prof/details/download-telecharger.cfm?Lang=E&SearchText=Canada&DGUIDlist=2021A000011124&GENDERlist=1,2,3&STATISTIClist=1&HEADERlist=0

title:
Census metropolitan areas (CMAs), tracted census agglomerations (CAs) and census tracts (CTs)	CSV
Note:
Census metropolitan area: (CMA) at least 100,000 of which 50,000 or more live in the core
Census agglomeration(CAs): have a core population of at least 10,000.
Census tracts (CTs): are small, relatively stable geographic areas that usually have a population between 2,500 and 8,000 persons.

You could get meta.txt and notes.txt from description in the zip file.

## boundary file
download from Statistics Canada->Census->Census of Population -> Census geography -> census tracts
link for 2016. 
https://www12.statcan.gc.ca/census-recensement/2011/geo/bound-limit/bound-limit-2016-eng.cfm

link for 2021
https://www12.statcan.gc.ca/census-recensement/2021/geo/sip-pis/boundary-limites/index2021-eng.cfm?year=21
choose Statistical boundaries/Census tracts
note: Census tracts only contains boundary of tracts. in census file, there's data about Census metropolitan areas and Census agglomeration which are higher level. these data are also imported but has no boundary (this is correct. otherwise, one lat,lng will has more than one matched records,might cause trouble when display)
 
download Shapefile (.shp) for boundary, then use qgis change the shp file to geojson
steps:
  1. open shp
  2. in menu, find layer, ->save layer
  3. save to geojson. check result. might need select Extent when save if format of geometry in geojson is incorrect.


these files are now in appd3/home/<USER>

# import
1、copy boundary file, census data file to server
scp -P 27 ./census2016/2016_census_data.csv.gz  appd3@*************:/mnt/files/appd3/boundary
scp -P 27 ./boundary/gmlbounday.geojson  appd3@*************:/mnt/files/appd3/boundary

2、 unzip 2016_census_data.csv.gz

3、import boundary files
./start.sh batch/boundary/importBoundary.coffee census2016 force /mnt/files/appd3/boundary/

4. http://www.test:8080/boundary?col=census2016Error
(边界如果有交叉，建立geo spacial index时会出错)
手动调整censor的error boundary，存到censor collection. 

4 import keys
./start.sh batch/census2016/processKeys.coffee ./

5. import notes
./start.sh batch/census2016/processNotes.coffee ./

4、import census data
./start.sh batch/census2016/processCensus.coffee /mnt/files/appd3/boundary/


=======参考命令=======

2.import census2016 data
import census boundary
copy data
scp -P 1023 ./gmlbounday.geojson  appd3@*************:~
scp -P 1023 ./2016_census_data.csv.gz  appd3@*************:~

./start.sh batch/boundary/importBoundary.coffee census2016 force ~/import_do_not_remove/boundary/
census 也做了压缩。
import keys
./start.sh batch/census2016/processKeys.coffee ~/import_do_not_remove/boundary/
./start.sh batch/census2016/processNotes.coffee ~/import_do_not_remove/boundary/

最后import census数据
./start.sh batch/census2016/processCensus.coffee ~/import_do_not_remove/boundary/

