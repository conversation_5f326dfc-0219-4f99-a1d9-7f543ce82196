1. 给bounday加check error tag
nohup ./start.sh batch/tag/addTagToProperty.coffee init  boundary checkError force >> ./logs/proptagLocError.log 2>&1 &
如果中间出错，继续
nohup ./start.sh batch/tag/addTagToProperty.coffee init  boundary checkError force useCheckPoint >> ./logs/proptagLocError.log 2>&1 &

2.检查有多少错误数据
db.getCollection('properties').count({provErr:{$ne:null}})
10732
grep -o 'error' proptagLocError.log | wc -l
7496
grep -o 'prov error of TR' proptagLocError.log | wc -l
2013
grep -o 'prov error of DDF' proptagLocError.log | wc -l
5328

3. unset 要导入的数据的_i, lat,lng, uaddr
# 如果加上unsetProvErr，会把provErr字段去掉，暂时不用。

./start.sh batch/tag/unsetLocError.coffee deleteGeoCache
find error geocoding in geoCache 5349, 肯定错误的
find uaddr but lat and lng is not same count: 5383， 不一定错误的。

4.重新导入treb import 和crea import。
./start.sh import/treb_import.coffee
./start.sh import/crea_import.coffee
Resume 8331/8331 Speed: 17.816091954022987/sDone 8331/8331

5. 检查还有多少错误，provErrOnly 参数会只查询有provErr的properties
nohup ./start.sh batch/tag/addTagToProperty.coffee init  boundary provErrOnly force >> ./logs/proptagLocError2.log &
grep -o 'error' proptagLocError2.log | wc -l


6.手动unset provErr
db.getCollection('properties').updateMany({provErr:{$ne:null}},{$unset:{provErr:1}})


=============other helper command ====
db.getCollection('properties').count({provErr:{$ne:null},src:'DDF'})
8368
db.getCollection('properties').count({provErr:{$ne:null},src:'TRB'})
2037
db.getCollection('properties').count({provErr:{$ne:null}})
10732

db.getCollection('mls_treb_master_records').count({$or:[{_i:{$exists:false}},{_i:false}]})
13634
db.getCollection('mls_crea_ddf_records').count({$or:[{_i:{$exists:false}},{_i:false}]})
11
db.getCollection('properties').count({provErr:{$ne:null},lat:{$exists:false}})
10087
> db.getCollection('properties').count({lat:{$exists:false}})
217727