## About
this repo can use new config to run
link: https://bitbucket.org/fredxiang/rmconfig/

## new config test list

## ONLINE STEPS: 
1. 确认新config appweb位置(mkdir /opt/rmappweb)，确认config位置(git clone rmprodcfg)， log位置 (/home/<USER>/logs/rmapplogs)
2. 确认新config的port和旧的不一样(ca1/shf3a -> 7084)
3. 准备https://bitbucket.org/fredxiang/rmprodcfg/src/master/, 从现有prod更新
4. 准备ca1m.local/shf3.local/ca3m.local， 更新到3台机器
5. kill旧batch 1by1，在新config启动batch，如果遇到问题rollback用旧config
6. 在新config 跑【add_pyfpimg】，&state=propDetail
7. Finally: redirect nginx port if new config is okay

## 按照功能区分
[x] 发送邮件/PN
[x] 登录 （除了微信,d2测试没问题，应该是vpn问题）
[x]school显示分享 （除了微信）
[x]mapSearch/ES
[x]分享 各种房源,各种方法
[x]第三方登录(微信 google apple facebook) （除了微信）
[x]第三方分享
[x]geocoder,local+remote
[x]所有页面地图显示
[x]fub报名
[x]forum分享,评论 contentMod
[]crime数据导入
[x]翻译功能,app内切换语言
[x]prop.notes查看分享
[x]autocomplete
[x]evaluation+similarProp 
[x]我的房源
[x]showing查看分享
[x]weather 模块
[x]userFile上传下载文件
[x]edm设置页面
[x]网站site_map
[x]所有的在cronjob里的batch, （遇到问题，fixed)
[x]所有的import的脚本和下载图片脚本 treb/crea/oreb/bcre

