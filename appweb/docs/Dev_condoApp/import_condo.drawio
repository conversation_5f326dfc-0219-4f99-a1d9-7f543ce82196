<mxfile host="65bd71144e">
    <diagram id="OezO1vNLsbPvkdLMqvWz" name="第 1 页">
        <mxGraphModel dx="1046" dy="493" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="25" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="2" target="23">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="properties按uaddr分组,查询mt大于指定日期,ptype2为Arpartment,uaddr下房源数大于5的uaddr列表-&amp;gt;uaddrs" style="whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
                    <mxGeometry x="240" y="50" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="3" target="4">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="解析uaddrs[i]中的cnty,prov,city" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="289" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="condos表存在当前uaddr记录coA?" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="379" width="180" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="19" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="5" target="7">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="按uaddr在properties中查找最近更新的1000个房源列表-&amp;gt;props" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="479" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="7" target="8">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="解析props,获取&lt;span style=&quot;color: rgb(206, 145, 120); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;amen,&lt;/span&gt;&lt;br&gt;&lt;span style=&quot;color: rgb(206, 145, 120); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;prop_mgmt&lt;/span&gt;&lt;span style=&quot;color: rgb(206, 145, 120); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;,&lt;/span&gt;&lt;span style=&quot;color: rgb(206, 145, 120); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;amen_extra&lt;br&gt;&lt;/span&gt;&lt;span style=&quot;background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;等统计信息-&amp;gt;&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(156, 220, 254); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;raw&lt;/span&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="579" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="21" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#FFFFFF;" edge="1" parent="1" source="8" target="11">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="对&lt;span style=&quot;color: rgb(156, 220, 254); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;raw&lt;/span&gt;筛选出符合条件的数据&lt;br&gt;&lt;span style=&quot;background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;&lt;font color=&quot;#ffffff&quot;&gt;-&amp;gt;&lt;/font&gt;&lt;/span&gt;&lt;span style=&quot;color: rgb(156, 220, 254); background-color: rgb(31, 31, 31); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;simple&lt;/span&gt;" style="whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="250" y="679" width="180" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=#FFFFFF;rounded=0;" edge="1" parent="1" source="10" target="5">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="605" y="504"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="condos&lt;br&gt;新建记录coA" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="520" y="377" width="170" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="22" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontColor=#FFFFFF;" edge="1" parent="1" source="11" target="12">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="condos_log&lt;br&gt;更新/新建记录&lt;span style=&quot;color: rgb(156, 220, 254); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;raw&lt;/span&gt;&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;到表中&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="255" y="789" width="170" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="12" target="29">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="730" y="952"/>
                            <mxPoint x="730" y="230"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="12" value="condos&lt;br&gt;更新记录&lt;span style=&quot;color: rgb(156, 220, 254); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;simple&lt;/span&gt;&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(31, 31, 31);&quot;&gt;到condoA&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="255" y="919" width="170" height="65" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="4" target="5">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="469" as="sourcePoint"/>
                        <mxPoint x="460" y="469" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="15" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" connectable="0" vertex="1" parent="14">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-4" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="4" target="10">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="360" y="469" as="sourcePoint"/>
                        <mxPoint x="460" y="469" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="17" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" connectable="0" vertex="1" parent="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="26" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="23" target="24">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="i = 0" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="280" y="140" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="i &amp;lt; uaddrs.length?" style="rhombus;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="270" y="210" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="27" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="24" target="3">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="470" y="240" as="sourcePoint"/>
                        <mxPoint x="470" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="28" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" connectable="0" vertex="1" parent="27">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-4" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="31" style="edgeStyle=none;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;fontColor=#FFFFFF;" edge="1" parent="1" source="29" target="24">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="i++" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="545" y="215" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="end" style="rounded=1;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" vertex="1" parent="1">
                    <mxGeometry x="40" y="210" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FFFFFF;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="24" target="32">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="270" y="240" as="sourcePoint"/>
                        <mxPoint x="370" y="240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="34" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;rounded=0;fontColor=#FFFFFF;" connectable="0" vertex="1" parent="33">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>