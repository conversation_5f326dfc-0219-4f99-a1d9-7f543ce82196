从Equifax获取个人信用报告

### Equifax文档
#### 获取token
```
curl -L -X POST 'https://api.equifax.ca/v2/oauth/token' \
-H 'Content-Type: application/x-www-form-urlencoded' \
-H 'Authorization: Basic {encodedString}' \
--data-urlencode 'scope={SCOPE}' \
--data-urlencode 'grant_type=client_credentials'

{
"access_token": "eyJraWQiOiJqYWZkdzdqUFhCTDdGeW9PTmppZHBGeTBEMEl-
2cVhDK0loSHg5NkJQaERZPSIsImFsZyI6IlJTMjU2In0.eyJzdWIiOiIybG12cnZo-
Z200a3RmNDdsNmtqOXZtMjdzMiIsInRva2VuX3VzZSI6ImFjY2VzcyIsInNjb3BlIjoidHJhbnNhY3Rpb25zXC9leHRlcm5hbCIsImF1dGhfdGltZSI6MTU5ODU1OD
MzNSwiaXNzIjoiaHR0cHM6XC9cL2NvZ25pdG8taWRwLmNhLWNlbnRyYWwtMS5hbWF6b25hd3MuY29tXC9jYS-
1jZW50cmFsLTFfWmVQN0hwRnQ2IiwiZXhwIjoxNTk4NTYxOTM1LCJpYXQiOjE1OTg1NTgzMzUsInZlcnNpb24iOjIsImp0aSI6Ijc1YzQzZTE5LWZlOGYtNGI0Yy04
ZWY2LTE1MjE0NmZiMGFhYyIsImNsaWVudF9pZCI6IjJsbXZydmhnbTRrdGY0N2w2a2o5dm0yN3MyIn0.
SdOypdpulOrKBTbRKnmqwnOSkvPCSiZpKC6yoyqaq9RuYS3JXUXnOASPARdREMaxqSKZGHA69Wd0x1YNEUjr0d_
yLj1mYENcU4F0eJ0I6MckQ53cPOKuT3q8Vweq4VpzhKCCaOKwiV7S1p4wDpNzqrVNM9IOrWPWnbZqcBDOsEJ-
KwlAX-n9aUtMFHKFhKSqU3qjM42ZUF1VZVNpY7c6l1-
4sGu9lYk3_XbHK2UNbfiw57XY33GIsFDCtF5DMQfgVG_FgqTj-Y-uYW1oPd-
XHIgq6PQpcLFZ4DXfcSvV_pTA1lWn4DU3uxfyT3sxUmrhI1vpr_-D1_aWFEUISWPOpw",
"expires_in": 3600,
"token_type": "Bearer"
}
```
#### 获取报告
```
EFX10000125201A01PKSS001059 <?xml version="1.0" encoding="UTF-8"?><CNCustTransmitToEfx xmlns="http://www.equifax.ca
/XMLSchemas/Production"><CNCustomerInfo><CustomerCode>xxxx</CustomerCode><CustomerInfo><CustomerNumber>xxx
xxxxxxx</CustomerNumber><SecurityCode>xx</SecurityCode></CustomerInfo><CustomerId>Clients</CustomerId></CNCusto
merInfo><CNRequests><CNConsumerRequests><CNConsumerRequest><Subjects><SubjectsubjectType="SUBJSubjectName><
LastName>TESTEVANS</LastName><FirstName>ALPHONSO</FirstName></SubjectName><SocialInsuranceNumber></SocialIns
SocialInsuranceNumber><DateOfBirth>1977-03- 02</DateOfBirth><Occupation></ Occupation></Subject><Addresses><Address
addressType="CURR"><CivicNumber>150</CivicNumber><StreetName>GraydonHallDrive</StreetName>< City>Toronto</City><
Provincecode="ON"/><PostalCode>M3A3B3</PostalCode></Address></Addresses></Subjects><CustomerReferenceNumber>2495
</CustomerReferenceNumber>< ECOAInquiryType>I</ECOAInquiryType><JointAccessIndicator>N</JointAccessIndicator></CNCo
nsumerRequest></CNConsumerRequests></CNRequests></CNCustTransmitToEfx>
```


### description
1. 获取用户输入的信息，调用Equifax api获取报告。
2. 具体需求见 docs/Change_requirements/20240726_add_equifax.md


### equifax collection
需要单独一个mongo数据库
| field      | data type      | description
| ---------- | -------------- | ---------------------------------
| _id        | string         | 自动生成
| ln         | string         | last name
| fn         | string         | first name
| sin        | string         | social insurance number
| dob        | string         | date of birth
| occ        | string         | Occupation                  
| civic      | string         | Civic number                
| unit       | string         | Unit number                 
| street     | string         | Street name                 
| city       | string         | City                        
| prov       | string         | Province                    
| zip        | string         | Postal code                 
| inXml      | string         | Input XML                   
| outXml     | string         | Output XML                  
| outJson    | string         | Output JavaScript object
| uid        | objectId       | user _id
| score      | string         | Personal reputation score
| status     | string         | Current record status
| errmsg     | string         | Error information obtained by sending the report
| m          | string         | Memo
| pic        | string         | Applicant Consent Agreement image