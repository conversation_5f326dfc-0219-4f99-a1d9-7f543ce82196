# Summary:
根据参数判断显示什么样的表格信息
根据输入值，生成用户的条件，到mapping里去找对应的。

# design:
## input：
hasWechat, 
src:['landLordRent','project','rmlisting','mls','evaluation']
uid, # ownerid
prop:{addr,city,sid, prov}

## return：
return 
{ok:0,needLogin:true}
return {ok:1,ret:{UIType:[],formDest:'projectToCrm',nm,eml,placeHolder,mbl,avt}}
formDest will be used as form type.['landLordRent','project','mls','rmlisting','evaluation','projectToCrm','upgradeVip']


## 
# validKeys
