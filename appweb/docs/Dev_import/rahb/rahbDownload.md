# RAHB Download and Import

## Data Source
We use Bridge RESO API to fetch listing of REALTORS Association of Hamilton-Burlington
https://bridgedataoutput.com/docs/platform/Introduction

`src/libapp/bridgeReso.coffee` provide function to call Bridge RESO API

## Download
`src/mlsImport/rahbDownload.coffee` is use for download RAHB properties then record will save to `rni.mls_rahb_records`, and every data row will also save to `rni.mls_rahb_logs` for logging purpose.

`src/libapp/rahbDownloadOfficeAndMember.coffee` is use for download RAHB Offices and Members, then save to `vow.agents` and `vow.offices`, and every data row will also save to `rni.mls_rahb_office_logs` and `rni.mls_rahb_member_logs` for logging purpose.

### Incremental Updates
In Bridge RESO API, we use `BridgeModificationTimestamp` field to use for incremental updates, last timestamp used will save in `rni.mls_import_keys`, for example:
```
{
  _id: "RahbOfficeAndMemberDownload",
  _mt: ISODate(2023-03-13T17:56:02.184Z),
  next: ISODate(2023-03-13T17:48:33.865Z)
}
```
`next` field mean the last time updated use `2023-03-13T17:48:33.865Z` as query timestamp, so next query will use `2023-03-13T17:48:33.865Z` as query timestamp (`$filter=BridgeModificationTimestamp gt 2023-03-13T17:48:33.865Z`), after update task findish, `next` will update to timestamp that task start.

## Import
During import, `saveToMaster.coffee` calls convert function from `src/libapp/impMappingRahb.coffee` to convert raw RAHB record to properties.

Hamilton have lots of subcity, the `city` field may be subcity name, they will correct to "Hamilton" when we do `formatProvAndCity`, check `subCityNameList` in `src/lib/staticCityFields.coffee`.
