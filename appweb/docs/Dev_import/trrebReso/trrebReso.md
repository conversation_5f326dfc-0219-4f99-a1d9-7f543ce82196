### trreb <PERSON>so 说明
[官方文档](https://developer.ampre.ca/docs/getting-started)

1. treb 数据查询：ListingKey eq 'C9264368'
2. oreb/car 数据（只能用filter）查询：OriginatingSystemKey eq '1412514'； OriginatingSystemKey eq '40669175'
3. [直接使用ID查询](`{{WebApiAddress}}/odata/Property('C9264368')`) 要比 [使用filter查询](`{{WebApiAddress}}/odata/Property?$filter=ListingKey eq 'C9264368'`)能获取更多的字段，随机查询5个prop，增加的54个字段的值都是null
4. 使用时间戳filter获取的，也是较少字段的结果。
5. evow数据中的商业，已给出价钱。
6. 文档中缺少openhouse，可以使用这种方式查询`{{WebApiAddress}}/odata/OpenHouse?$top=100&$filter=ListingKey eq 'E11197658'`