<mxfile host="65bd71144e">
    <diagram id="DuttrdPG0ojrmyfFY_kd" name="第 1 页">
        <mxGraphModel dx="1261" dy="926" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="17" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="2" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="input prop" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="30" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;formatSqft&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到sqft1,sqft2&lt;/font&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="120" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="prop.m?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="220" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="10" target="11" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="获取prop.m中所有的数字" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="340" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="prop.sqft1?" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="440" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="20" y="507" width="550" height="155" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;getSqftsFromMatchedNumbersWithRange&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到结果 &lt;/font&gt;&lt;font color=&quot;#ffffff&quot;&gt;sqftFromProp&amp;nbsp;= {&lt;/font&gt;&lt;span style=&quot;color: rgb(212, 212, 212);&quot;&gt;sqfts,estSqfts&lt;/span&gt;&lt;span style=&quot;color: rgb(212, 212, 212);&quot;&gt;,sqft,estSqft}&lt;/span&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;" parent="15" vertex="1">
                    <mxGeometry x="200" y="70" width="350" height="85" as="geometry"/>
                </mxCell>
                <mxCell id="14" value="sqfts: 面积数组(m中满足条件)&lt;br&gt;estSqfts: 估算的面积数组(m中可能满足)&lt;br&gt;sqft: 面积(sqfts最小值)&lt;br&gt;estSqft: 估算面积(estSqfts只有一个值时存在)" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;direction=south;align=left;verticalAlign=top;size=20;" parent="15" vertex="1">
                    <mxGeometry width="220" height="90" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="&lt;span style=&quot;color: rgb(255, 255, 255); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;sqftFromProp.&lt;/span&gt;sqft？" style="rhombus;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="305" y="744" width="180" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="9" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="470" y="250" as="sourcePoint"/>
                        <mxPoint x="570" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="23" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="22" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="25" value="" style="endArrow=classic;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="11" target="13" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="470" y="530" as="sourcePoint"/>
                        <mxPoint x="570" y="530" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="26" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="25" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="27" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="13" target="16" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="34" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="31" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="31" value="result = {rmSqft:sqft, src:'self', sqftQ:'1}" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="285" y="1600" width="220" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="33" value="return result" style="rounded=1;whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="320" y="1714" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FFFFFF;" parent="1" source="35" target="44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="sqfts_aggregate&lt;br&gt;查找 _id，得到unitSqft" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="640" y="920" width="220" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="39" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontColor=#FFFFFF;rounded=0;" parent="1" source="36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="400" y="713" as="targetPoint"/>
                        <mxPoint x="735" y="293" as="sourcePoint"/>
                        <Array as="points">
                            <mxPoint x="735" y="713"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="36" value="&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;sqftFromProp = null&lt;/span&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="660" y="450" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="" style="endArrow=classic;html=1;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;rounded=0;" parent="1" source="9" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="420" y="400" as="sourcePoint"/>
                        <mxPoint x="520" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="38" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="37" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="40" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="16" target="31" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="420" y="840" as="sourcePoint"/>
                        <mxPoint x="520" y="840" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="40" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint y="-312" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;&quot;&gt;unitSqft.sqfts.length?&lt;/span&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="650" y="1025" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontColor=#FFFFFF;" parent="1" source="45" target="46" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;pickOneSqft&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到{rmSqft,rmSqft1,rmSqft2}&lt;/font&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="632.5" y="1143" width="235" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="110" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="46" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="result = {rmSqft,rmSqft1,rmSqft2,src:'unit',sqftQ:2}" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="635" y="1595" width="230" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="44" target="45" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="760" y="1135" as="sourcePoint"/>
                        <mxPoint x="860" y="1135" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="50" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="49" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;sqftFromProp.esSqft?&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="960" y="1025" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="54" value="" style="endArrow=classic;html=1;rounded=0;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="44" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1030" y="1095" as="sourcePoint"/>
                        <mxPoint x="1130" y="1095" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="55" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontColor=#FFFFFF;" parent="54" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="111" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="56" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="400" y="1720" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1060" y="1680"/>
                            <mxPoint x="395" y="1680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="56" value="&lt;font style=&quot;font-size: 12px;&quot;&gt;result = {&lt;br&gt;rmSqft:&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;sqftFromProp.esSqft,&lt;/span&gt;&lt;br style=&quot;&quot;&gt;src:'unit',&lt;br&gt;sqftQ:3}&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;fontSize=12;" parent="1" vertex="1">
                    <mxGeometry x="945" y="1580" width="230" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="57" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="53" target="56" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1030" y="1195" as="sourcePoint"/>
                        <mxPoint x="1130" y="1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="58" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="57" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="60" value="&lt;span style=&quot;font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;unitSqft.estSqfts.length?&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1220" y="1025" width="250" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="61" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="53" target="60" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1030" y="1195" as="sourcePoint"/>
                        <mxPoint x="1130" y="1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="62" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="61" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="67" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="63" target="64" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="63" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;pickOneSqft&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到{rmSqft,rmSqft1,rmSqft2}&lt;/font&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1227.5" y="1143" width="235" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="115" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="64" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1345" y="1680"/>
                            <mxPoint x="395" y="1680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="64" value="result = {rmSqft,rmSqft1,rmSqft2,src:'unit',sqftQ:4}" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1230" y="1590" width="230" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="65" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="60" target="63" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1030" y="1195" as="sourcePoint"/>
                        <mxPoint x="1130" y="1195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="66" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="65" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="73" value="prop.unit?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="675" y="744" width="150" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="74" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="16" target="73" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="790" y="840" as="sourcePoint"/>
                        <mxPoint x="890" y="840" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="74" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="88" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="79" target="83" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="79" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;splitLevelEnding&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到结果&amp;nbsp;&lt;/font&gt;levelEnding =&amp;nbsp;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;{level,ending,levelNum,unit}&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="930" y="744" width="260" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="80" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="73" target="79" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="790" y="820" as="sourcePoint"/>
                        <mxPoint x="890" y="820" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="81" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="80" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="86" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="82" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="82" value="_id = prop.uaddr" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="672.5" y="840" width="155" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="83" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="83" value="&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;&quot;&gt;_id&lt;/span&gt; = &lt;span style=&quot;&quot;&gt;prop&lt;/span&gt;.&lt;span style=&quot;&quot;&gt;uaddr&lt;/span&gt;+&lt;span style=&quot;color: rgb(206, 145, 120);&quot;&gt;'#'&lt;/span&gt;+levelEnding.unit&lt;br&gt;&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="920" y="840" width="280" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="84" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="73" target="82" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="790" y="950" as="sourcePoint"/>
                        <mxPoint x="890" y="950" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="85" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="84" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="89" value="prop.unt?&lt;br&gt;&amp;amp;&amp;amp;&lt;br&gt;prop.saft1?&lt;br&gt;&amp;amp;&amp;amp;&lt;br&gt;prop.ptye2 has&amp;nbsp;&lt;span style=&quot;color: rgb(206, 145, 120); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;'Apartment'&lt;/span&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;?&lt;br&gt;&amp;amp;&amp;amp;&lt;br&gt;levelEnding.ending?&lt;br&gt;&lt;/span&gt;" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1560" y="995" width="340" height="120" as="geometry"/>
                </mxCell>
                <mxCell id="104" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="90" target="91" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="90" value="&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;&quot;&gt;_id&lt;/span&gt; = &lt;span style=&quot;&quot;&gt;prop&lt;/span&gt;.&lt;span style=&quot;&quot;&gt;uaddr&lt;/span&gt;+&lt;span style=&quot;color: rgb(206, 145, 120);&quot;&gt;'&lt;/span&gt;&lt;span style=&quot;color: rgb(206, 145, 120);&quot;&gt;@&lt;/span&gt;&lt;span style=&quot;color: rgb(206, 145, 120);&quot;&gt;'&lt;/span&gt;+levelEnding.ending&lt;/div&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1590" y="1158" width="280" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="98" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="91" target="92" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="91" value="sqfts_aggregate&lt;br&gt;查找 _id，得到&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;endingSqft&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" parent="1" vertex="1">
                    <mxGeometry x="1620" y="1245" width="220" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="92" value="&lt;span style=&quot;color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;endingSqft&lt;/span&gt;&lt;span style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;.sqft?&lt;/span&gt;&lt;span style=&quot;color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; background-color: rgb(30, 30, 30);&quot;&gt;&lt;br&gt;&lt;/span&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;||&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: rgb(212, 212, 212);&quot;&gt;endingSqft.estSqft?&lt;/span&gt;&lt;br&gt;&lt;/div&gt;" style="rhombus;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1630" y="1360" width="200" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="93" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="94" target="95" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="94" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;pickOneSqft&lt;/span&gt;&lt;/div&gt;&lt;div style=&quot;background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; line-height: 18px;&quot;&gt;&lt;font color=&quot;#dcdcaa&quot;&gt;得到{rmSqft,rmSqft1,rmSqft2}&lt;/font&gt;&lt;/div&gt;" style="shape=process;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1612.5" y="1470" width="235" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="116" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="95" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="1730" y="1680"/>
                            <mxPoint x="395" y="1680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="95" value="result = {rmSqft,rmSqft1,rmSqft2,src:'ending',sqftQ:5}" style="rounded=0;whiteSpace=wrap;html=1;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1600" y="1590" width="260" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="96" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="92" target="94" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1800" y="1440" as="sourcePoint"/>
                        <mxPoint x="1900" y="1440" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="97" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="96" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="100" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="60" target="89" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1690" y="1240" as="sourcePoint"/>
                        <mxPoint x="1790" y="1240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="101" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="100" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="102" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="89" target="90" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1690" y="1240" as="sourcePoint"/>
                        <mxPoint x="1790" y="1240" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="103" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="102" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="117" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="1" source="105" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="2070" y="1680"/>
                            <mxPoint x="395" y="1680"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="105" value="result = null" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1980" y="1600" width="180" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="106" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="92" target="105" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1960" y="1450" as="sourcePoint"/>
                        <mxPoint x="2060" y="1450" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="107" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="106" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-85" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="108" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="89" target="105" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1690" y="1280" as="sourcePoint"/>
                        <mxPoint x="1790" y="1280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="109" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="108" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-80" y="-188" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="135" value="&lt;div style=&quot;color: rgb(212, 212, 212); background-color: rgb(30, 30, 30); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace; font-weight: normal; line-height: 18px;&quot;&gt;&lt;span style=&quot;color: #dcdcaa;&quot;&gt;pickOneSqft&lt;/span&gt;&lt;/div&gt;" style="swimlane;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="1" vertex="1">
                    <mxGeometry x="1320" y="10" width="860" height="980" as="geometry">
                        <mxRectangle x="1320" y="10" width="120" height="30" as="alternateBounds"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="146" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="136" target="137" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="136" value="input = {&lt;span style=&quot;background-color: rgb(30, 30, 30); color: rgb(212, 212, 212); font-family: Menlo, Monaco, &amp;quot;Courier New&amp;quot;, monospace;&quot;&gt;unitSqft,prop}&lt;/span&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="40" width="150" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="137" value="prop.sqft1?&lt;br&gt;||&lt;br&gt;prop.sqft2?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="110" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="138" value="unitSqft.sqfts.length?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="190" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="139" value="unitSqft.estSqfts.length?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="220" y="190" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="154" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="140" target="142" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="140" value="筛选在sqft1和sqft2之间的数，得到数组 sqfts" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="20" y="270" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="141" value="筛选在sqft1和sqft2之间的数，得到数组 sqfts" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="225" y="430" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="142" value="unitSqft.estSqfts.length?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="340" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="159" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="143" target="144" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="143" value="筛选在sqfts中的数，得到数组 sqftsInEst" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="20" y="430" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="144" value="sqftsInEst.length?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="510" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="176" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="145" target="169" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="145" value="sqfts = sqftsInEst" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="20" y="600" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="147" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="137" target="138" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="370" y="270" as="sourcePoint"/>
                        <mxPoint x="470" y="270" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="148" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="147" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="152" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="138" target="140" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="130" y="250" as="sourcePoint"/>
                        <mxPoint x="130" y="280" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="153" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="152" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="155" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="142" target="143" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="190" y="390" as="sourcePoint"/>
                        <mxPoint x="190" y="420" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="156" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="155" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="157" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="139" target="141" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="400" y="260" as="sourcePoint"/>
                        <mxPoint x="400" y="290" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="158" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="157" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="160" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="144" target="145" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="180" y="570" as="sourcePoint"/>
                        <mxPoint x="180" y="600" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="161" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="160" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="164" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="135" source="138" target="139" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="190" y="190" as="sourcePoint"/>
                        <mxPoint x="475" y="190" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="165" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="164" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="169" value="sqfts.length?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="680" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="170" value="sqfts.length === 1?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="15" y="770" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="171" value="sqfts.length === 2?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="230" y="770" width="150" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="181" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="172" target="175" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="172" value="result = {rmSqft:sqfts[0]}" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="20" y="850" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="174" value="(sqfts.max - sqfts.min)&amp;lt; &lt;br&gt;(0.2 * sqfts.min)?" style="rhombus;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="440" y="767.5" width="186" height="55" as="geometry"/>
                </mxCell>
                <mxCell id="175" value="return result" style="rounded=1;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="20" y="930" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="177" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="169" target="170" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="150" y="750" as="sourcePoint"/>
                        <mxPoint x="150" y="790" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="178" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="177" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="179" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="170" target="172" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="200" y="820" as="sourcePoint"/>
                        <mxPoint x="200" y="860" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="180" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="179" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="182" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="135" source="170" target="171" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="170" y="770" as="sourcePoint"/>
                        <mxPoint x="170" y="810" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="183" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="182" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="184" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="135" source="171" target="174" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="390" y="810" as="sourcePoint"/>
                        <mxPoint x="390" y="850" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="185" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="184" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="200" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="186" target="175" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="533" y="910"/>
                            <mxPoint x="90" y="910"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="186" value="result = {rmSqft:sqfts.min,&lt;br&gt;rmSqft1:sqfts.min,&lt;br&gt;rmSqft2:sqfts.max}" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="463" y="850" width="140" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="199" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="187" target="175" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="187" value="result = null" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="235" y="850" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="188" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="171" target="187" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="230" y="820" as="sourcePoint"/>
                        <mxPoint x="295" y="820" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="189" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="188" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="190" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="135" source="174" target="186" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="440" y="830" as="sourcePoint"/>
                        <mxPoint x="500" y="830" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="191" value="yes" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="190" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="192" value="result = null" style="rounded=0;whiteSpace=wrap;html=1;fontSize=12;fontColor=#FFFFFF;" parent="135" vertex="1">
                    <mxGeometry x="678" y="850" width="140" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="193" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="135" source="174" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="760" as="sourcePoint"/>
                        <mxPoint x="725" y="760" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="194" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="193" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="195" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="135" source="137" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="460" y="140" as="sourcePoint"/>
                        <mxPoint x="582" y="195" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="196" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="195" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-220" y="-66" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="197" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="135" source="139" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="510" y="210" as="sourcePoint"/>
                        <mxPoint x="632" y="265" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="198" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="197" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-220" y="-129" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="203" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;edgeStyle=orthogonalEdgeStyle;" parent="135" source="169" target="192" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="220" y="700" as="sourcePoint"/>
                        <mxPoint x="342" y="755" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="204" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="203" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="208" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="141" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="345" y="540" as="sourcePoint"/>
                        <mxPoint x="90" y="660" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="295" y="660"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="209" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fontSize=12;fontColor=#FFFFFF;" parent="135" source="192" target="175" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="660" y="910" as="sourcePoint"/>
                        <mxPoint x="445" y="950" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="210" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="142" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1510" y="370" as="sourcePoint"/>
                        <mxPoint x="1410" y="670" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1520" y="375"/>
                            <mxPoint x="1520" y="670"/>
                            <mxPoint x="1420" y="670"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="211" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="210" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-20" y="-185" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="212" value="" style="endArrow=classic;html=1;rounded=0;fontSize=12;fontColor=#FFFFFF;exitX=1;exitY=0.5;exitDx=0;exitDy=0;edgeStyle=orthogonalEdgeStyle;" parent="1" source="144" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1550" y="560" as="sourcePoint"/>
                        <mxPoint x="1410" y="670" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="1520" y="545"/>
                            <mxPoint x="1520" y="670"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="213" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;fontSize=12;fontColor=#FFFFFF;" parent="212" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="-20" y="-100" as="offset"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="214" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="11" target="36" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="630" y="410" as="sourcePoint"/>
                        <mxPoint x="730" y="410" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="215" value="no" style="edgeLabel;resizable=0;html=1;align=center;verticalAlign=middle;" parent="214" connectable="0" vertex="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="216" value="Rain: 这里不需要写具体的代码逻辑，写概括的商业逻辑。如：1从面积数组和估值数组选择合适的数组，2筛选数组...。3返回....&lt;br&gt;代码会变但是大体逻辑不变" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=west;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=20;pointerEvents=1;" vertex="1" parent="1">
                    <mxGeometry x="1180" y="10" width="140" height="160" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>