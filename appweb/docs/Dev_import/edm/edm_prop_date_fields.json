{"L_ListingID": "System ID", "L_Class": "Class", "L_Type_": "Property Class", "L_Area": "Region", "L_SystemPrice": "Search Price", "L_AskingPrice": "List Price", "L_AddressNumber": "Address Number", "L_AddressSearchNumber": "Address Search Number", "L_AddressDirection": "Address Direction", "L_AddressStreet": "Address Street", "L_City": "Area/City", "L_State": "Province", "L_Zip": "Postal Code", "L_NumAcres": "Total Acreage", "L_ListAgent1": "Listing Agent 1", "L_ListOffice1": "Listing Firm 1", "L_ListAgent2": "Listing Agent 2", "L_ListOffice2": "Listing Firm 2", "L_ListAgent3": "Listing Agent 3", "L_ListOffice3": "Listing Firm 3", "L_ListingDate": "List Date", "L_InputDate": "Input Date", "L_UpdateDate": "Last Transaction Date", "L_PictureCount": "Listing Firm 3", "L_Last_Photo_updt": "PhotoTimestamp", "L_OffMarketDate": "Off Market Date", "L_DisplayId": "Listing ID #", "L_AddressNumberLow": "Address Number Low", "L_address_number_low_search": "Address Search Number Low", "L_AddressSuite": "Address Suite", "L_AddressSuiteSearch": "Address Search Suite", "L_StreetDesignationId": "Street Designation", "L_AddressPostDirection": "Address Post Direction", "L_FirstPhotoAddDt": "First Photo Add Timestamp", "L_Address": "Address", "L_Status": "Status", "L_DOM": "DOM", "L_DOMLS": "DOMLS", "L_CDOM": "Cumulative DOM", "L_CDOMLS": "Cumulative DOMLS", "L_PricePerSQFT": "Price Per SQFT", "LM_Char1_4": "Seller Rights Res Y/N", "LM_Char1_6": "Disclosure Report Y/N", "LM_Char1_8": "Agricultural Y/N", "LM_Char1_10": "New Home on Old Lot Y/N", "LM_Char1_11": "Secondary Suite Y/N", "LM_Char1_12": "2nd Suite Permit <PERSON>", "LM_Char1_13": "Remodelled Y/N", "LM_Char1_14": "Fireplace Y/N", "LM_Char1_15": "Garage Y/N", "LM_Char1_16": "Living Room Level", "LM_Char1_17": "Dining Room Level", "LM_Char1_18": "Kitchen Level", "LM_Char1_19": "Family Room Level", "LM_Char10_2": "Legal Lot", "LM_Char10_4": "Nearest Town", "LM_Char10_13": "Style", "LM_Char10_17": "Grade Level Entry Y/N", "LM_Char10_25": "Legal Plan", "LM_Char10_26": "Unit Factor", "LM_Char10_28": "Foundation", "LM_Char10_29": "Parking Plan Description", "LM_Char25_2": "Community", "LM_Char25_3": "Land Use/Zoning", "LM_Char25_4": "Lot Dimensions Info", "LM_Char25_5": "Basement", "LM_Char25_6": "Basement Development", "LM_Char25_7": "Garage Dimensions", "LM_Char25_8": "Living Room Dimensions", "LM_Char25_9": "Dining Room Dimensions", "LM_Char25_10": "Kitchen Dimensions", "LM_Char25_11": "Family Room Dimensions", "LM_Char25_12": "Den Dimensions", "LM_Char25_13": "Bonus Room Dimensions", "LM_Char25_14": "Master Bedroom Dimensions", "LM_Char25_15": "Bedroom 2 Dimensions", "LM_Char25_16": "Bedroom 3 Dimensions", "LM_Char25_17": "Bedroom 4 Dimensions", "LM_Char25_18": "Other Room 1 Dimensions", "LM_Char25_19": "Other Room 2 Dimensions", "LM_Char25_20": "Other Room 3 Dimensions", "LM_Char25_21": "Other Room 4 Dimensions", "LM_Char25_22": "Other Room 5 Dimensions", "LM_Char25_23": "Other Room 6 Dimensions", "LM_Char25_24": "Parking Unit", "LM_Char25_25": "Property Management Phone", "LM_Char50_2": "Closest Elementary School", "LM_Char50_3": "Closest Jr High School", "LM_Char50_4": "Closest Sr High School", "LM_Char50_5": "Other School", "LM_Int1_1": "Minimum Age", "LM_Int1_2": "# Finished Levels", "LM_Int1_3": "Enclosed Parking", "LM_Int1_4": "Total Parking", "LM_Int1_5": "Additional Rooms", "LM_Int1_6": "Total Bedrooms", "LM_Int1_7": "# of Buildings", "LM_Int1_8": "# of Docks", "LM_Int1_9": "# of Elevators", "LM_Int1_10": "# of Grade Doors", "LM_Int1_11": "# of Loading Doors", "LM_Int1_13": "2-pc Ensuite Bath", "LM_Int1_14": "3-pc Ensuite Bath", "LM_Int1_15": "4-pc Ensuite Bath", "LM_Int1_16": "5-pc Ensuite Bath", "LM_Int1_17": "6-pc Ensuite Bath", "LM_Int1_18": "Half Baths", "LM_Int1_19": "Full Baths", "LM_Int1_20": "Floor Number", "LM_Int2_3": "Local Improvement Year", "LM_Int2_4": "Conforming Cert Year", "LM_Int2_5": "Remodelled Year", "LM_Int2_8": "Total Floors (of Bldg)", "LM_Int2_9": "Total Units", "LM_Int2_10": "Parking Unit Factor", "LM_Int2_12": "Year Built", "LM_Int2_19": "Bedrooms Above Grade", "LM_Int4_2": "Local Improvement Amount", "LM_Int4_3": "Developer/Builder Size", "LM_Int4_4": "Lot Depth (ft)", "LM_Int4_8": "Rooms Above Grade", "LM_Int4_9": "# of Parcels", "LM_Dec_1": "Lot Sq Metres", "LM_Dec_2": "Net Lease Rate SF/Annum", "LM_Dec_3": "Lease Op Cost SqFt", "LM_Dec_5": "Tot Flr Area AG(Metric)", "LM_Dec_6": "Flr Area Above (Metric)", "LM_Dec_7": "Flr Area Below Grd (m2)", "LM_Dec_8": "Flr Area Lower (Metric)", "LM_Dec_9": "Flr Area Main Lev (m2)", "LM_Dec_10": "Flr Area Upper (Metric)", "LM_char1_21": "Den Level", "LM_char1_22": "Bonus Room Level", "LM_char1_23": "Master Bedroom Level", "LM_char1_24": "Bedroom 2 Level", "LM_char1_25": "Bedroom 3 Level", "LM_char1_26": "Bedroom 4 Level", "LM_char1_27": "Other Room 1 Level", "LM_char1_28": "Other Room 2 Level", "LM_char1_29": "Other Room 3 Level", "LM_char1_30": "Other Room 4 Level", "LM_char1_31": "Other Room 5 Level", "LM_char1_32": "Other Room 6 Level", "LM_char1_33": "Ensuite", "LM_char1_34": "School Bus Y/N", "LM_char1_35": "HOA Fee Y/N", "LM_char1_36": "End Unit Y/N", "LM_char1_37": "Balcony/Terrace", "LM_char1_38": "Elevator Y/N", "LM_char1_39": "Parking Plan Type", "LM_char1_40": "Reserve Fund Study Y/N", "LM_char1_45": "Reg Water Rights (Y/N)", "LM_char5_2": "Legal Block", "LM_char5_3": "Title to Land", "LM_char5_5": "City Quadrant", "LM_char5_6": "Title Storage Y/N", "LM_char5_7": "Zone", "LM_char5_8": "Building Type", "LM_char5_9": "Range", "LM_char5_10": "<PERSON>", "LM_char5_11": "Front Exposure", "LM_char5_12": "Conforming Report Type", "LM_char5_13": "Efficiency Rating", "LM_char5_14": "Construction Type", "LM_char5_15": "Roof Type", "LM_char5_16": "Garage Door Height", "LM_char5_17": "Garage <PERSON> Width", "LM_char5_18": "Other Room 1", "LM_char5_19": "Other Room 2", "LM_char5_20": "Other Room 3", "LM_char5_21": "Other Room 4", "LM_char5_22": "Other Room 5", "LM_char5_23": "Other Room 6", "LM_char5_24": "HOA Fee Payment Sched", "LM_char5_25": "Condo Fee Payment Sched", "LM_char5_26": "Floor Location", "LM_char5_27": "Water Paid for Desc", "LM_char5_28": "Heating Source Desc", "LM_char5_29": "Phone Desc", "LM_char10_59": "Parking Stall #", "LM_char50_6": "Condo Name", "LM_char50_7": "Property Management", "LM_char50_17": "Land Use Code", "LM_char100_4": "Linc #", "LM_char100_5": "Intersection", "LM_char100_6": "Address Display", "LM_Dec_14": "List Pr / SqFt", "LM_Dec_15": "Lease Addl Rent per SF", "LM_Dec_16": "Flr Area Below Grd (SF)", "LM_Dec_17": "Flr Area Lower (SF)", "LM_Dec_18": "Land Hectares", "LM_Dec_19": "Land Sq Meter", "LM_Dec_22": "Total Flr Area (SF)", "LM_Dec_23": "# of Storeys", "LM_Dec_24": "<PERSON><PERSON><PERSON>", "LM_DateTime_6": "Reserve Fund Date", "LM_bit_1": "Separate Entrance Field Y/N", "LM_Dec_31": "<PERSON><PERSON>", "LM_Dec_32": "HOA Fee", "VT_VTourURL": "URL-Virtual Tour", "VT_ExtVTourURL3": "URL-Addl Images", "VT_ExtVTourURL5": "URL-AlternateFeatureSheet", "VT_ExtVTourURL2": "URL-Map", "VT_ExtVTourURL1": "URL-Brochure", "LFD_OWNERSHIPINTEREST_1": "Ownership Interest", "LFD_RESTRICTIONS_3": "RESTRICTIONS", "LFD_WARRANTY_4": "WARRANTY", "LFD_HEATINGTYPE_5": "HEATING TYPE", "LFD_HEATINGSOURCE_6": "HEATING SOURCE", "LFD_GOODSINCLUDED_7": "GOODS INCLUDED", "LFD_EXTERIOR_8": "EXTERIOR", "LFD_FLOORING_9": "FLOORING", "LFD_REMODELLED_10": "REMODELLED", "LFD_FIREPLACETYPE_11": "FIREPLACE TYPE", "LFD_FIREPLACEFUEL_12": "FIREPLACE FUEL", "LFD_PARKING_13": "PARKING", "LFD_SITEINFLUENCES_14": "SITE INFLUENCES", "LFD_AMENITIESFEATURES_15": "AMENITIES/FEATURES", "LFD_HOAFEEINCLUDES_16": "HOA FEE INCLUDES", "LFD_CONDOFEEINCLUDES_17": "CONDO FEE INCLUDES", "LFD_UNITEXPOSURE_18": "UNIT EXPOSURE", "LFD_WATERSUPPLY_20": "WATER SUPPLY", "LFD_SEWERSEPTIC_21": "SEWER/SEPTIC", "LFD_POWERSERVICE_22": "POWER SERVICE", "LFD_ROADACCESS_104": "ROAD ACCESS", "LR_remarks33": "Directions", "LR_remarks44": "Attached Goods Excluded", "LA1_LoginName": "LA1Agent Logon Name", "LA1_UserFirstName": "LA1Agent First Name", "LA1_UserLastName": "LA1Agent Last Name", "LA1_UserMI": "LA1Agent Middle Initial", "LA1_AddressStreet": "LA1AgentAddressStreetName", "LA1_City": "LA1Agent City", "LA1_Zip": "LA1Agent Zip", "LA1_PhoneNumber1Desc": "LA1AgentPhone1Description", "LA1_PhoneNumber1CountryCodeId": "LA1Agent Phone1 CountryId", "LA1_PhoneNumber1": "LA1Agent Phone1 Number", "LA1_PhoneNumber1Ext": "LA1Agent Phone1 Extension", "LA1_PhoneNumber2Desc": "LA1AgentPhone2Description", "LA1_PhoneNumber2CountryCodeId": "LA1Agent Phone2 CountryId", "LA1_PhoneNumber2": "LA1Agent Phone2 Number", "LA1_PhoneNumber2Ext": "LA1Agent Phone2 Extension", "LA1_PhoneNumber3Desc": "LA1AgentPhone3Description", "LA1_PhoneNumber3CountryCodeId": "LA1Agent Phone3 CountryId", "LA1_PhoneNumber3": "LA1Agent Phone3 Number", "LA1_PhoneNumber3Ext": "LA1Agent Phone3 Extension", "LA1_PhoneNumber4Desc": "LA1AgentPhone4Description", "LA1_PhoneNumber4CountryCodeId": "LA1Agent Phone4 CountryId", "LA1_PhoneNumber4": "LA1Agent Phone4 Number", "LA1_PhoneNumber4Ext": "LA1Agent Phone4 Extension", "LA1_PhoneNumber5Desc": "LA1AgentPhone5Description", "LA1_PhoneNumber5CountryCodeId": "LA1Agent Phone5 CountryId", "LA1_PhoneNumber5": "LA1Agent Phone5 Number", "LA1_PhoneNumber5Ext": "LA1Agent Phone5 Extension", "LA1_Email": "LA1Agent Email", "LA1_WebPage": "LA1Agent Url", "LA1_AgentID": "LA1User Code", "LA2_LoginName": "LA2Agent Logon Name", "LA2_UserFirstName": "LA2Agent First Name", "LA2_UserLastName": "LA2Agent Last Name", "LA2_UserMI": "LA2Agent Middle Initial", "LA2_AddressStreet": "LA2AgentAddressStreetName", "LA2_Address2": "LA2Agent Address2", "LA2_City": "LA2Agent City", "LA2_Zip": "LA2Agent Zip", "LA2_PhoneNumber1Desc": "LA2AgentPhone1Description", "LA2_PhoneNumber1CountryCodeId": "LA2Agent Phone1 CountryId", "LA2_PhoneNumber1": "LA2Agent Phone1 Number", "LA2_PhoneNumber1Ext": "LA2Agent Phone1 Extension", "LA2_PhoneNumber2Desc": "LA2AgentPhone2Description", "LA2_PhoneNumber2CountryCodeId": "LA2Agent Phone2 CountryId", "LA2_PhoneNumber2": "LA2Agent Phone2 Number", "LA2_PhoneNumber2Ext": "LA2Agent Phone2 Extension", "LA2_PhoneNumber3Desc": "LA2AgentPhone3Description", "LA2_PhoneNumber3CountryCodeId": "LA2Agent Phone3 CountryId", "LA2_PhoneNumber3": "LA2Agent Phone3 Number", "LA2_PhoneNumber3Ext": "LA2Agent Phone3 Extension", "LA2_PhoneNumber4Desc": "LA2AgentPhone4Description", "LA2_PhoneNumber4CountryCodeId": "LA2Agent Phone4 CountryId", "LA2_PhoneNumber4": "LA2Agent Phone4 Number", "LA2_PhoneNumber4Ext": "LA2Agent Phone4 Extension", "LA2_PhoneNumber5Desc": "LA2AgentPhone5Description", "LA2_PhoneNumber5CountryCodeId": "LA2Agent Phone5 CountryId", "LA2_PhoneNumber5": "LA2Agent Phone5 Number", "LA2_PhoneNumber5Ext": "LA2Agent Phone5 Extension", "LA2_Email": "LA2Agent Email", "LA2_WebPage": "LA2Agent Url", "LA2_AgentID": "LA2User Code", "LA3_LoginName": "LA3Agent Logon Name", "LA3_UserFirstName": "LA3Agent First Name", "LA3_UserLastName": "LA3Agent Last Name", "LA3_UserMI": "LA3Agent Middle Initial", "LA3_AddressStreet": "LA3AgentAddressStreetName", "LA3_Address2": "LA3Agent Address2", "LA3_City": "LA3Agent City", "LA3_Zip": "LA3Agent Zip", "LA3_PhoneNumber1Desc": "LA3AgentPhone1Description", "LA3_PhoneNumber1CountryCodeId": "LA3Agent Phone1 CountryId", "LA3_PhoneNumber1": "LA3Agent Phone1 Number", "LA3_PhoneNumber1Ext": "LA3Agent Phone1 Extension", "LA3_PhoneNumber2Desc": "LA3AgentPhone2Description", "LA3_PhoneNumber2CountryCodeId": "LA3Agent Phone2 CountryId", "LA3_PhoneNumber2": "LA3Agent Phone2 Number", "LA3_PhoneNumber2Ext": "LA3Agent Phone2 Extension", "LA3_PhoneNumber3Desc": "LA3AgentPhone3Description", "LA3_PhoneNumber3CountryCodeId": "LA3Agent Phone3 CountryId", "LA3_PhoneNumber3": "LA3Agent Phone3 Number", "LA3_PhoneNumber3Ext": "LA3Agent Phone3 Extension", "LA3_PhoneNumber4Desc": "LA3AgentPhone4Description", "LA3_PhoneNumber4CountryCodeId": "LA3Agent Phone4 CountryId", "LA3_PhoneNumber4": "LA3Agent Phone4 Number", "LA3_PhoneNumber4Ext": "LA3Agent Phone4 Extension", "LA3_PhoneNumber5Desc": "LA3AgentPhone5Description", "LA3_PhoneNumber5CountryCodeId": "LA3Agent Phone5 CountryId", "LA3_PhoneNumber5": "LA3Agent Phone5 Number", "LA3_PhoneNumber5Ext": "LA3Agent Phone5 Extension", "LA3_Email": "LA3Agent Email", "LA3_WebPage": "LA3Agent Url", "LA3_AgentID": "LA3User Code", "LO1_BranchOfOrgID": "LO1Main Office ID", "LO1_ShortName": "LO1Office Abbreviation", "LO1_OrganizationName": "LO1Office Name", "LO1_OrgAddressStreet": "LO1OffceAddressStreetName", "LO1_OrgAddress2": "LO1Office Address2", "LO1_OrgCity": "LO1Office City", "LO1_OrgZip": "LO1Office Zip", "LO1_PhoneNumber1Desc": "LO1OfficePhone1Descriptin", "LO1_PhoneNumber1CountryCodeId": "LO1OfficePhone1CountryId", "LO1_PhoneNumber1": "LO1Office Phone1 Number", "LO1_PhoneNumber1Ext": "LO1OfficePhone1Extension", "LO1_EMail": "LO1Office Email", "LO1_WebPage": "LO1Office Url", "LO1_board_id": "LO1Board ID", "LO2_BranchOfOrgID": "LO2Main Office ID", "LO2_ShortName": "LO2Office Abbreviation", "LO2_OrganizationName": "LO2Office Name", "LO2_OrgAddressStreet": "LO2OffceAddressStreetName", "LO2_OrgAddress2": "LO2Office Address2", "LO2_OrgCity": "LO2Office City", "LO2_OrgZip": "LO2Office Zip", "LO2_PhoneNumber1Desc": "LO2OfficePhone1Descriptin", "LO2_PhoneNumber1CountryCodeId": "LO2OfficePhone1CountryId", "LO2_PhoneNumber1": "LO2Office Phone1 Number", "LO2_PhoneNumber1Ext": "LO2OfficePhone1Extension", "LO2_EMail": "LO2Office Email", "LO2_WebPage": "LO2Office Url", "LO2_board_id": "LO2Board ID", "LO3_BranchOfOrgID": "LO3Main Office ID", "LO3_ShortName": "LO3Office Abbreviation", "LO3_OrganizationName": "LO3Office Name", "LO3_OrgAddressStreet": "LO3OffceAddressStreetName", "LO3_OrgAddress2": "LO3Office Address2", "LO3_OrgCity": "LO3Office City", "LO3_OrgZip": "LO3Office Zip", "LO3_PhoneNumber1Desc": "LO3OfficePhone1Descriptin", "LO3_PhoneNumber1CountryCodeId": "LO3OfficePhone1CountryId", "LO3_PhoneNumber1": "LO3Office Phone1 Number", "LO3_PhoneNumber1Ext": "LO3OfficePhone1Extension", "LO3_EMail": "LO3Office Email", "LO3_WebPage": "LO3Office Url", "LO3_board_id": "LO3Board ID", "LV_vow_include": "Internet Display Y/N", "LV_vow_address": "Display Address Y/N", "L_IdxInclude": "Broker Reciprocity Flag", "L_Keyword2": "Water Report Year", "L_Keyword3": "Sewer/Septic Yr Built", "L_Keyword4": "2nd Res Living Area (SF)", "LM_Char1_9": "2nd Residence Y/N", "LM_Char10_12": "West Meridian", "LM_Char10_18": "Meridian 2", "LM_Char10_19": "Meridian 3", "LM_Char10_20": "Meridian 4", "LM_Char10_21": "Meridian 5", "LM_Char10_30": "Storage Unit #", "LM_char1_44": "Farm Equip Included Y/N", "LM_char1_46": "Irrigation Equipment Y/N", "LM_char1_47": "Condo Fee Y/N", "LM_char5_4": "Title Storage Y/N", "LM_char5_30": "Township", "LM_char5_31": "Section", "LM_char5_32": "Quarter", "LM_char5_33": "Quarter Section", "LM_char5_34": "Range 2", "LM_char5_35": "Township 2", "LM_char5_36": "Section 2", "LM_char5_37": "Quarter 2", "LM_char5_38": "Quarter Section 2", "LM_char5_39": "Range 3", "LM_char5_40": "Township 3", "LM_char5_41": "Section 3", "LM_char5_42": "Quarter 3", "LM_char5_43": "Quarter Section 3", "LM_char5_44": "Range 4", "LM_char5_45": "Township 4", "LM_char5_46": "Section 4", "LM_char5_47": "Quarter 4", "LM_char5_48": "Quarter Section 4", "LM_char5_49": "Range 5", "LM_char5_50": "Township 5", "LM_char5_51": "Section 5", "LM_char5_52": "Quarter 5", "LM_char5_53": "Quarter Section 5", "LM_char5_55": "Major Use Description", "LM_char50_10": "Land Last Used As", "LM_char100_2": "Distance to Public Trans", "LM_char100_7": "Distance to Nearest Town", "LM_int4_38": "Depth of Well", "LM_int4_39": "Water GPM", "LM_Dec_12": "2nd Res Liv Area (Metric)", "LM_Dec_21": "List Price/Acre", "LM_Dec_25": "Acres Cleared", "LM_Dec_26": "Acres Cultivated", "LM_Dec_27": "<PERSON><PERSON><PERSON>", "LM_Dec_30": "Acres Water Rights", "LFD_OWNERSHIPINTEREST_24": "Ownership Interest", "LFD_RESTRICTIONS_26": "RESTRICTIONS", "LFD_WARRANTY_27": "WARRANTY", "LFD_HEATINGTYPE_28": "HEATING TYPE", "LFD_HEATINGSOURCE_29": "HEATINGSOURCE", "LFD_GOODSINCLUDED_30": "GOODS INCLUDED", "LFD_EXTERIOR_31": "EXTERIOR", "LFD_FLOORING_32": "FLOORING", "LFD_REMODELLED_33": "REMODELLED", "LFD_FIREPLACETYPE_34": "FIREPLACE TYPE", "LFD_FIREPLACEFUEL_35": "FIREPLACE FUEL", "LFD_PARKING_36": "PARKING", "LFD_SITEINFLUENCES_37": "SITE INFLUENCES", "LFD_AMENITIESFEATURES_38": "AMENITIES/FEATURES", "LFD_WATERSUPPLY_39": "WATER SUPPLY", "LFD_SEWERSEPTIC_40": "SEWER/SEPTIC", "LFD_ROADACCESS_41": "ROAD ACCESS", "LFD_POWERSERVICE_42": "POWER SERVICE", "LFD_OUTBUILDING_43": "OUT BUILDING", "LFD_HOAFEEINCLUDES_46": "HOA FEE INCLUDES", "LFD_CONDOFEEINCLUDES_47": "CONDO FEE INCLUDES", "LFD_UNITEXPOSURE_49": "UNIT EXPOSURE", "L_Keyword6": "Lease <PERSON> (Mo)", "L_Keyword7": "License Fee", "LM_char50_8": "Mobile Home Park", "LM_char50_9": "Mobile Home Make", "LM_char50_11": "Mobile Home Model", "LFD_OWNERSHIPINTEREST_51": "Ownership Interest", "LFD_RESTRICTIONS_53": "RESTRICTIONS", "LFD_WARRANTY_54": "WARRANTY", "LFD_HEATINGTYPE_55": "HEATING TYPE", "LFD_HEATINGSOURCE_56": "HEATINGSOURCE", "LFD_GOODSINCLUDED_57": "GOODS INCLUDED", "LFD_EXTERIOR_58": "EXTERIOR", "LFD_FLOORING_59": "FLOORING", "LFD_REMODELLED_60": "REMODELLED", "LFD_FIREPLACETYPE_61": "FIREPLACE TYPE", "LFD_FIREPLACEFUEL_62": "FIREPLACE FUEL", "LFD_PARKING_63": "PARKING", "LFD_SITEINFLUENCES_64": "SITE INFLUENCES", "LFD_AMENITIESFEATURES_65": "AMENITIES/FEATURES", "LM_Char10_6": "Sale Type", "LM_Char10_10": "Major Business Type", "LM_Char10_11": "Minor Business Type", "LM_Char10_14": "Transaction Type", "LM_Char10_23": "Lease Type", "LM_Char25_27": "Lease Term (in Months)", "LM_Int2_15": "Business Operating Since", "LM_Int2_20": "# of Units", "LM_char5_56": "Env Assessmt Phase", "LM_int4_23": "Parking - # of Spaces", "LM_int4_24": "Above-Main Ofc Space", "LM_int4_26": "Office Area Sq Ft", "LM_int4_28": "Retail Area Sq Ft", "LM_int4_29": "Warehouse Sq Ft", "LM_int4_30": "Subj Prop Retail Sz", "LM_int4_32": "Subject Space SqFt", "LM_int4_35": "Land Size SF", "LM_int4_40": "Lot Frontage (ft)", "LM_Dec_11": "Subject Space Width", "LM_Dec_28": "Land Size Acres", "LM_Dec_29": "Clear Ceiling Ht (Feet)", "LFD_OWNERSHIPINTEREST_67": "Ownership Interest", "LFD_RESTRICTIONS_69": "RESTRICTIONS", "LFD_OTHERPROPERTYTYPES_73": "OTHER PROPERTY TYPES", "LFD_BUILDINGTYPE_74": "BUILDING TYPE", "LFD_CONSTRUCTION_75": "CONSTRUCTION", "LFD_BUSINESSPRICEINCLUDES_76": "BUSINESS PRICE INCLUDES", "LFD_FIREPROTECTION_80": "FIRE PROTECTION", "LFD_HEATINGSOURCE_82": "HEATINGSOURCE", "LFD_HVAC_83": "HVAC", "LFD_POWERTYPE_84": "POWER TYPE", "LFD_REPORTSAVAILABLE_85": "REPORTS AVAILABLE", "LFD_SEWERSEPTIC_88": "SEWER/SEPTIC", "LFD_SITESERVICES_90": "SITE SERVICES", "LFD_UNITSINCLUDE_91": "UNITS INCLUDE", "LFD_SITEINFLUENCES_92": "SITE INFLUENCES", "LFD_AMENITIESFEATURES_93": "AMENITIES/FEATURES", "LFD_WATERSUPPLY_97": "WATER SUPPLY", "LR_remarks11": "Assessment Type"}