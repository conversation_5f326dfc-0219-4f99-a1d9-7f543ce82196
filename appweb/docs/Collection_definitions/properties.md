# All fields of properties collection

| Name | Source | Description | Example |
|-|-|-|-|
| AmmenitiesNearBy | DDF | | "Public Transit" |
| AppointmentPhoneNumber | BRE | | "************" |
| Board | DDF | | 114 |
| BrochureLink | DDF | | http://timesrealty.forsalebyowner.ca/mls/apartment-for-sale-new-westminster-BC/131022?utm_source=realtor.ca&utm_medium=Referral&utm_campaign=TR-MLS |
| Building | DDF | | `{ "FireplacePresent" : "False", "SizeInterior" : "421 sqft", "UtilityWater" : "Municipal water" }` |
| BuildingType | DDF | | "Offices" |
| Business | DDF | | `{ "Franchise": "" }` |
| BusinessSubType | DDF | | "Retail misc." |
| BusinessType | DDF, RHB | | "Retail and Wholesale" |
| Class | BRE | | "Land" |
| CommunicationType | DDF | | "DSL" |
| CommunityFeatures | DDF | | "Quiet Area" |
| ComplexSubdivisionName | BCRE | | "Red Bluff" |
| Crop | DDF | | "Fruits" |
| Displayid | BRE | | "R2569464" |
| Easement | DDF | | "Flood Plain" |
| EquipmentType | DDF | | "Water Heater" |
| FakeListing | BRE | | "No" |
| FarmType | DDF | | "Orchard" |
| Features | DDF | | "Other" |
| FloorAreaFinAbvMain | BRE | | 1,100 |
| FloorAreaFinBasement | BRE | | 0 |
| FloorAreaFinBlwMain | BRE | | 0 |
| FloorAreaFinMainFlr | BRE | | 1100 |
| FloorAreaFinTotal | BRE | | 2200 |
| ForAppointmentCall | BRE | | "William Lacy" |
| FrontageMetres | BRE | | 36.88 |
| FullBaths | BRE | | 2 |
| GstIncl | BRE | | "No "|
| HalfBaths | BRE | | 3 |
| IncludeInDdfyN | BRE | | "Yes" |
| Land | DDF | | `{ "SizeTotalText": "16.33x139.58 FT", "Acreage": "false", "Amenities": [ "Highway", "Public Transit" ], "SizeIrregular": "16.33x139.58 FT" }` |
| LastTransDate | BRE | | ISODate("2019-01-02T05:00:00Z") |
| Lease | DDF | | 4500 |
| LeasePerTime | DDF | | "Monthly" |
| LeaseType | DDF | | "Net" |
| ListingContractDate | DDF | | 20141011 |
| ListingEnteredBy | BRE | | "FTAMMARAS" |
| LiveStockType | DDF | | "Horse" |
| LoadingType | DDF | | "Other" |
| LocationDescription | DDF | | "Dundas to Erindale to Stainton" |
| LotSzsf | BRE | | 20740 |
| LotSzsqft | BRE | | 20740 |
| LotSzsqmtrs | BRE | | "1,926.81" |
| MaintenanceFeeType | DDF | | "Property Management" |
| MapLink | DDF: `AlternateURL.MapLink` | | "http://maps.google.ca/maps?q=14807+MARINE+DRIVE+White%20Rock+O0O+0O0+bc&spn=0.012065,0.040770&iwloc=A&hl=en" |
| MgmtCoName | BRE | | "Crossroads Management Ltd." |
| MgmtCoPhone | BRE | | "************" |
| OwnershipType | DDF | | "Strata" |
| Parking | DDF | | `[{ "Name": "Garage" }, {"Name": "Visitor Parking" }]` |
| ParkingSpaceTotal | DDF | | 1 |
| PhotoLink | TREB: `Tour_url` | | "http://bit.ly/2f5A2tn" |
| Plan | DDF | | "0727611" |
| PoolFeatures | DDF | | "Heated pool" |
| Price | DDF | | 1100000 |
| PricePerSqft | BRE | | 4.49 |
| PricePerUnit | DDF | | "acres" |
| PrivacyFlag | BRE | | "\*\*Privacy Prot\*\*" |
| PropType | BRE | | "Residential Detached" |
| PropertyType | DDF | | "Vacant Land"|
| RentalEquipmentType | DDF | | "Water Heater" |
| RightType | DDF | | "Water Rights" |
| RoadType | DDF | | "No thru road" |
| SignType | DDF | | "Signband" |
| SocialMediaWebsite | | | "http://www.fsbo.ca/mls/house-for-sale-coquitlam-BC/553376?utm_source=realtor.ca&utm_medium=Referral&utm_campaign=FRE-MLS" |
| SoldPricePerSqft | BRE | | 4.49 |
| SpLpRatio | BRE | | "0.65" |
| StorageType | DDF | | "Storage Shed" |
| Structure | DDF | | "Greenhouse" |
| TotUnitsInStrataPlan | BRE | | 60 |
| TransactionType | DDF | | "For lease" |
| Utilities | DDF: `UtilitiesAvailable` | | `[{ "t" : "Sewer", "m" : "Installed" }, { "t" : "Natural Gas", "m" : "Installed" }, { "t" : "Hydro", "m" : "Installed" }, { "t" : "Cable", "m" : "Available" }]` |
| View | BRE | | "No" |
| ViewSpecify | BRE | | "OCEAN & NORTH SHORE MOUNTAINS" |
| ViewType | DDF | | "Lake view" |
| WaterFrontName | DDF | | "CECEBE LAKE" |
| WaterFrontType | DDF | | "Waterfront" |
| ZoningDescription | DDF | | "r" |
| ZoningType | DDF | | "See Remarks" |
| _id | | primary key | "TRBC5481588" |
| _mt | | last updated timestamp | ISODate("2022-07-19T22:40:28.058Z") |
| aUaddr | | | "CANADA:ONTARIO:WELLAND:585 KING STREET" |
| ac | TREB: `Ac`, RHB: `CoolingYN` and `Cooling` | | "Central Air" |
| access_prop1 | TREB | | "Highway" |
| access_prop2 | TREB | | "Private Road" |
| access_prop_srch | TREB | | ".N." |
| acres | TREB | | "2-4.99" |
| addl_pix_url | TREB | | "http://www.studiogtavirtualtour.ca/109-10095-bramalea-road-brampton" |
| addr | TREB: `Addr`, DDF: `StreetAddress`, BRE: `Address`, RHB: split from `UnparsedAddress` | | "585 King St" |
| addr1 | DDF: `Address.AddressLine1` | | "585 KING ST" |
| age | TREB: `Yr_built`, DDF: `Building.Age`, BRE: `Age` | | "New" or "16-30" or "100+" |
| age1 | TREB: `Yr_built`, DDF: `Building.Age` | prop estimate age range upper bound | 16 |
| age2 | TREB: `Yr_built`, DDF: `Building.Age` | prop estimate age range upper bound | 30 |
| agent_id | TREB | | 9516551 |
| aid | TREB | agent id | "TRB1324383" |
| aid2 | TREB | agent id | "TRB9541751" |
| all_inc | TREB | | "N" |
| amen | TREB: `Bldg_amen1_out` + `Bldg_amen2_out` + ... | | `["Security System", "Outdoor Pool", "Recreation Room", "Sauna", "Tennis Court"]` |
| anaClk | DDF `AnalyticsClick` | | "<![CDATA[<script type='text/javascript'>\n function redirect() { \nvar event = new RlEvent('crealocation');\nevent.viewedProperty(19321144,22211,[1468389],[51259],[1529203],[161536]);\nevent.set('event', 'type', 'click');\nevent.record();\nsetTimeout(\"window.location = 'https://www.realtor.ca/PropertyDetails.aspx?PropertyId=19321144'\",500);}\n</script>]]>" |
| anaVw | DDF `AnalyticsView` | | "<![CDATA[<script src='http://analytics.crea.ca/crearl.js' type='text/javascript'></script>\n<script type='text/javascript'>\nvar event = new RlEvent('crealocation');\nevent.viewedProperty(14595119,15054,[1719286],[274708],[1719286],[0]);\nevent.set('event', 'type', 'view');\nevent.record();\n</script>]]>" |
| app_req | TREB | | "Y" |
| appts | TREB | | "905-898-1211" |
| apt_num | TREB | | "405" |
| arch_style | DDF: `ArchitecturalStyle` | | "Bungalow" |
| archive | | | true |
| area | | | "Halton" |
| area_code | TREB | | "06" |
| area_infl | TREB: `Area_infl1_out` + `Area_infl2_out` + ... | | `["Major Highway", "Public Transit"]` |
| asmt | TREB: `Tv` | | 183000 |
| ass_year | TREB | | 2016 |
| assessment | TREB | | 0 |
| assignment | TREB | | "N" |
| avgpicsz | TREB, DDF | | 16027.65 |
| balcony | TREB | | "None" |
| bcf | | f = Freehold, c = Condo | "f" |
| bid | TREB | broker id | "TRB001000" |
| blcny | TREB: `Patio_ter` | | "Open" |
| bldg_name | | | "Index" |
| bltYr | DDF: `Building.ConstructedDate`, RHB: `YearBuilt`, may be overwrite by `age` field at `impFormat.formatProp` | prop bulit year | 2013 |
| bltYr1 | realmaster: `propertyTagHelper.getPropertyBaseTag` | prop estimate bulit year lower bound | 2010 |
| bltYr2 | realmaster: `propertyTagHelper.getPropertyBaseTag` | prop estimate bulit year upper bound | 2015 |
| bm | BRE: `RealtorRemarks` | | "All measurements approximate, verify if important.  Lockbox on gas meter.  Subject to court approval." |
| bndCity | realmaster: `propertyTagHelper` | City boundary | `{ "_id": "CA:ON:OAKVILLE", "nm": "Oakville", "tp": "T", "sz": 1 }` |
| bndCmty | realmaster: `propertyTagHelper` | Community boundary | `{ "_id": "CA:ON:OAKVILLE:PALERMO WEST", "nm": "Palermo West" }` |
| bndProv | `propertyTagHelper` | Province boundary | `{ "_id": "CA:ON", "nm": "ON" }` |
| bndRegion | realmaster: `propertyTagHelper` | Region boundary | `{ "_id": "CA:ON:REGION HALTON", "nm": "Halton" }` |
| bndSubCity | realmaster: `propertyTagHelper` | Subcity boundary | `{ "_id": "CA:ON:SUBCITY TORONTO AND EAST YORK", "nm": "Toronto and East York" }` |
| bnds | | school boundary related data | [1020422,1024618,1028085,1025816,1025276,1020272,1029955,1029963] |
| board | DDF: `Board` mapping by ddfBoardMap | | "TREB" |
| boardName | BRE: `ShortRegionCode` | | "Real Estate Board of Greater Vancouver" |
| bph_num | TREB | deprecated, la.tel | "************" |
| br | TREB | above ground bedRooms | 2 |
| br_plus | TREB, BRE: `BdsInBsmt` | under ground bedrooms | 2 |
| bsmt | TREB: `Bsmt1_out` + `Bsmt2_out`, DDF: `Building.BasementDevelopment` + `Building.BasementFeatures` + `Building.BasementType`, RHB: `Basement` | basment | ["Fin"] |
| bsmt1_out | TREB | | "Full" |
| bsmt2_out | TREB | | "Part Fin" |
| bthrms | TREB: `Bath_tot`, DDF: `Building.BathroomTotal`, RHB: `BathroomsTotalInteger` | bathrooms | 2 |
| bths | TREB: parse by `Wcloset_*` fields | | `[{ "t": 1, "l": null, "p": 3 }, { "t": 1, "l": null, "p": 2 }]` |
| cac_inc | TREB | Centre Air Condition Included | "N" |
| calcBdrms | DDF: parse by `Building.Rooms` when prov is BC | | 4 |
| calcBr_plus | DDF: parse by `Building.Rooms` when prov is BC | | 2 |
| cases | realmaster `propAddress` | address parse cases | ["f"] |
| cd | TREB | Sold date | 20060317 |
| ceil_ht | TREB | | 9 |
| ceil_ht_in | TREB | | 0 |
| cert_lvl | TREB | | "Stat Cert Avail W/ Offer" |
| chattels | TREB | | "N" |
| city | TREB: `Municipality`, DDF: `Address.City`, BRE: `City`, RHB: `City` | | "Oakville" |
| city_d | TREB: `Municipality_district` | | "Oakville" |
| city_id | DDF: `MunicipalId` | | "06.04" |
| city_water | TREB | | "Y" |
| citycd | TREB: `Municipality_code` | | "06.04" |
| class | TREB | | "Free" |
| cldd | TREB: `Td` | Closing date | 20210210 |
| cmty | TREB: `Community`, DDF: `Address.Subdivision` or `Address.Neighbourhood` or `Address.CommunityName` | community, RHB: `Township` | "Palermo West" |
| cmtycd | TREB: `Community_code` | | "06.04.0010" |
| cndsold_xd | TREB | | "6/4/2018" |
| cnty | TREB: `nation` or "CA", DDF: `Address.Country`, BRE: fixed value "CA", RHB: `Country` | nation | "CA" |
| co_lagt_id | TREB | agent id | 9543063 |
| co_lagt_ph | TREB | agent telephone | "************" |
| co_list | TREB | agent name | "SANDRA SOLDERA, Broker" |
| code_treb | TREB | | 494701 |
| com_cn_fee | TREB | Com Cndo Fee | 199.31 |
| com_coopb | TREB | deprecated, replace by `comm` field | "2.5%" |
| comel_inc | TREB | Common Elements Included | "Y" |
| comm | TREB | | "2.5% + Hst" |
| commuId | | census community id | ["355350104.00"] |
| cond | TREB | | "Fin/Stat/Lease Agreement" |
| cond_txinc | TREB | | "N" |
| condo_corp | TREB | | "YCC" |
| constr | TREB: `Constr1_out` + `Constr2_out`, RHB: `ConstructionMaterials` | consrtction type, brick, concrete, log... | ["Brick"] |
| contac_exp | TREB | | "N" |
| coop | | | `{ "nm": "ROYAL SERVICE REAL ESTATE", "agnt": [ { "nm": "Tyler Smith" } ] }` |
| corp_num | TREB | | 16 |
| country | | deprecated | "Canada" |
| crane | TREB | | "N" |
| credit_chk | | | "Y" |
| crsst | TREB: `cross_st`, RHB: `CrossStreet` | Crossroad | "Dundas/Bronte" |
| cs16 | realmaster: `propertyTagHelper` | | "355350609.00" |
| ctrdt | | deprecated | 20150717 |
| cur | TREB: fixed value "CND" | | "CND" |
| daddr | TREB: `Disp_addr` | | "Y" |
| days_open | TREB | | 7 |
| dba | TREB | | "Kennedy Public House" |
| ddfID | | only exist when TREB prop merge another DDF prop | "DDF19321144" |
| ddf_idx | | | "Y" |
| den_fr | TREB | | "N" |
| depth | TREB | | 110 |
| distributeinternet | TREB | | "Y" |
| drive | TREB | | "Mutual" |
| dt_sus | TREB | | ISODate("1960-04-28T16:00:00Z") |
| dt_ter | TREB | | 19601105 |
| dta | "TREB" | | "d" |
| easement_rest1 | TREB | | |
| easement_rest1_out | TREB | | |
| easement_rest2 | TREB | | |
| easement_rest2_out | TREB | | |
| easement_rest3 | TREB | | |
| easement_rest3_out | TREB | | |
| easement_rest4 | TREB | | |
| easement_rest4_out | TREB | | |
| easement_rest_srch | TREB | | |
| elec | TREB | | "Y" |
| elevator | TREB | | "None" |
| employees | TREB | | 40 |
| emply_lett | TREB | | "Y" |
| energy_cert | TREB | | "N" |
| ens_lndry | TREB | | "N" |
| esc_flag | TREB | | "N" |
| exer_gym | TREB | | "None" |
| exp | BRE: `ExpiryDate` | Expire Date | 20210104 |
| faddr | realmaster: `propAddrHelper.getFullAddress` | full address | "2609 Valleyridge Dr, Oakville, ON L6M 5H6, Canada" |
| farm_agri | Treb | | "Land & Bldgs" |
| favU | | | |
| favUsr | | | |
| favcnt | | | 9 |
| fce | TREB: `condo_exp` or `comp_pts`, BRE: `AddressDirection`, RHB: `DirectionFaces` | Exposure | "W" |
| feat | TREB: `Prop_feat1_out` + ... | | ["Golf"] |
| fin_stmnt | TREB | | "N" |
| flt | TREB: `Front_ft` (and compute with `Lotsz_code`) | | 28 |
| fpl | TREB: `Fpl_num` | | "N" |
| franchise | TREB | | "N" |
| freestandg | TREB | | "Y" |
| frnshd | TREB: `Furnished` | Furnished | 0 |
| front_ft | TREB | | 28 |
| fuel | TREB: `Fuel`, DDF: `Building.HeatingFuel` | | "Natural gas" |
| furnished | TREB | | "N" |
| gas | TREB | | "Y" |
| gatp | TREB: `Gar_type` | Parking type | "Attached" |
| geoq | | geography coding (lat, lng and loc field) confidence level | 105 |
| gr | TREB: `Gar_spaces` or `Gar`, RHB: `GarageSpaces` | Parking garage | 1 |
| green_pis | TREB | | "N" |
| handi_equipped | TREB | | "N" |
| handicappedequipped | TREB | | "Y" |
| heat | TREB: `Heating`, DDF: `Building.HeatingType`, RHB: `Heating` | | "Forced Air" |
| heat_inc | TREB | | "Y" |
| his | realmaster | prop history | `[ { "s": "New", "ts": ISODate("2019-01-02T05:00:00Z") } ]` |
| holdover | TREB: `Holdover`, RHB: `RAHB_HoldoverDays` | | 360 |
| hours_open | TREB | | 89.5 |
| hydro_inc | TREB | Hydro Included | "Y" |
| id | | same as _id, may be deprecated? | "BREC8000066" |
| idx | TREB | | "x" |
| idx_dt | TREB | | "2013-01-16 09:42:51.0" |
| ind_area | TREB | | 1765 |
| ind_areacd | TREB | | "Sq Ft" |
| input_date | TREB | | 20060317 |
| insur_bldg | TREB | Building Insurance | "Y" |
| internet | TREB | | "Y" |
| irreg | TREB | | "3612 Dufferin 4 Sale 2 - W3926214" |
| kch | TREB: `Kit_total` or `Num_kit` + `Kit_plus`, BRE: `OfKitchens` | Kitchen | 1 |
| keepQ | | keepPropGeocoding | 1 |
| kit_plus | TREB | | 0 |
| kit_total | TREB | | 1 |
| la | TREB: `Co_list` + `Co_lagt_ph` + `Co_lagt_id` + `Rltr` + `Bph_num`, RHB: mapping by `officeMlsId` and `agentMlsId` | listing agent | `{"nm":"ROYAL SERVICE REAL ESTATE","tel":"************","agnt":[{"nm":"TYLER SMITH","tel":"************"}]}` |
| la2 | DDF: `AgentDetails` | listing agent | `{"agnt":[{"id":"1468389","nm":"YASIN YUSUFI","_id":"DDF1468389","pstn":"Broker","tel":"(*************","url":"http://www.yasinyusufi.ca,www.yasinyusufi.com","office":{"id":"51259","_id":"DDF51259","nm":"RE/MAX WEST REALTY INC.","city":"Toronto","prov":"ON","addr":"6074 KINGSTON ROAD","zip":"M1C1K4","tel":"(*************","fax":"(*************","url":"http://www.remaxwest.com"}}],"id":"51259","_id":"DDF51259","nm":"RE/MAX WEST REALTY INC.","city":"Toronto","prov":"ON","addr":"6074 KINGSTON ROAD","zip":"M1C1K4","tel":"(*************","fax":"(*************","url":"http://www.remaxwest.com"} `|
| laHis | | la history | |
| lagt_ph | TREB | | "************" |
| lastupdated | TREB | | ISODate("2022-04-13T14:39:55Z") |
| lat | DDF: `Address.Latitude`, RHB: `Latitude`, realmaster geoCoder | latitude | 43.43245 |
| laundry | TREB | | "Ensuite" |
| laundry_lev | TREB | laundry level | "Main" |
| lcN | realmaster | last price change new value | 888000 |
| lcO | realmaster | last price change old value | 988000 |
| lcTp | realmaster | last change type: "Pc" = price change, "Chg" = status change | "Pc" |
| ld | BRE: `ListDate` | | 20201002 |
| lease | TREB | | "Y" |
| lease_term | TREB | | "1 Year" |
| legal_desc | TREB | | "Lot 122-3 Plan 20M-930" |
| link | DDF: `MoreInformationLink` | | "https://www.realtor.ca/PropertyDetails.aspx?PropertyId=19321144" |
| link_comment | TREB | | "This Is A Linked Property" |
| link_yn | TREB | | "N" |
| list_agent | TREB | | "YASIN YUSUFI, Broker" |
| lkr | TREB: `Locker` | Locker | |
| llbo | TREB | | "N" |
| llq | | deprecated, lat/lng quality | 100 |
| lng | DDF: `Address.Longitude`, RHB: `Longitude`, realmaster geoCoder | longitude | -79.7783 |
| loc | realmaster geoCoder | App use this field to display properties location on map | { "type": "Point", "coordinates": [ -79.7783, 43.43245 ]} |
| locker_lev_unit | TREB | | "P4" |
| locker_num | TREB | | "B29" |
| locker_unit | TREB | | 22 |
| lot_code | | | |
| lotsz_code | | | |
| lp | TREB: `Lp_dol`, DDF: `Price` or `Lease`, RHB: `ListPrice` | sale price | 595000 |
| lpr | DDF: `Price` or `Lease` | lease price | |
| lpunt | TREB: `lp_code`, DDF : `LeasePerUnit` | | "For Lease" |
| lsddt | | deprecated | "10/07/2016" |
| lsdp | | | "$1,550" |
| lse_terms | TREB | | "24 Months" |
| lsrc | TREB | | "evow" |
| lst | TREB: `lsc`, BRE: `Status`, RHB: compute by `MlsStatus` and `StandardStatus` | | "Sus" |
| ltp | TREB: fixed value "MLS" | listing type | "MLS" |
| lup | DDF: `@attributes.LastUpdated`, RHB: `BridgeModificationTimestamp` | last update timestamp (from source) | ISODate("2019-01-02T05:00:00Z") |
| lvl | TREB: `stories`, RHB: `Stories` | level | 5 |
| m | TREB: `Ad_text` + `Extras`, DDF: `PublicRemarks`, BRE: `PublicRemarks`, RHB: `PublicRemarks` | message | "New Mattamy Build Freehold Never Lived In,Open Concept, Eat In Kitchen,Close To Major Highways And Shopping. Also For Sale" |
| m_zh | | chinese message | "主要交通位置。位于公路401（Dufferin出口/入口）约克代尔和Costco之间。还有待售3614 Right隔壁。土地面积约为2335平方尺，每层800平方英尺及地下室" |
| manmt | TREB | | ISODate("2022-08-20T06:30:18.854Z") |
| maxPicSz | crea_download_pic | | 0 |
| maxpicsz | TREB | | 28793 |
| merged | | _id of another prop that this prop merged with, exist only if this prop merged to another prop | "TRBW5743907" |
| metaInfo | | | {} |
| mfee | TREB: `Addl_mo_fee` or `maint`, DDF: `MaintenanceFee`, BRE: `MaintenanceFee`, RHB: `MaintenanceExpense` | maintenance fee | 765.23 |
| mfeeunt | DDF: `MaintenanceFeePaymentUnit` | | "Monthly" |
| mintrm | TREB: `Minrenttrm`, RHB: `RAHB_MinRentalTerm` | Min Term(Mth) | 36 |
| mmap_col | TREB | | 20 |
| mmap_page | TREB | | 470 |
| mmap_row | TREB | | "N" |
| mort_comm | TREB | | "Clear" |
| mt | | last updated timestamp | ISODate("2006-03-17T05:00:00Z") |
| noGeocoding | realmaster | no geo coding flag, set this flag while import if prop older than 2 years | true |
| num_kit | TREB | Kitchen | 1 |
| oa_area | TREB | | 0 |
| occupancy | TREB | | "Vacant" |
| offD | | unavailable date | 20160317 |
| off_areacd | TREB | | "Sq Ft" |
| offerD | realmaster | | 20201006 |
| oh | TREB | deprecated, use ohz | |
| oh0 | TREB | deprecated, use ohz | |
| oh0t | TREB | deprecated, use ohz | |
| oh_date | TREB | deprecated, use ohz | |
| ohz | compute from TREB: `Oh_date${n}` + `Oh_from${n}` + `Oh_to${n}` + `Oh_link${n}` + `Oh_type${n}`, DDF: `OpenHouse.Event` | open house time, tp is type, P = physical, V = virtual | [{ "f": "2022-10-01 14:00", "t": "2022-10-01 16:00", "tp": "P" }] |
| olp | TREB: `Orig_dol`, BRE: `OriginalPrice`, RHB: `OriginalListPrice` | | 1600 |
| onD | | avaliable date | 19691231 |
| opt_to_buy | | | "N" |
| origAddr | | if addr after parsed not same as original data source, save original addr to this field | "50A Lakeshore Rd W" |
| origBsmt | | if bsmt after parsed not same as original data source, save original bsmt to this field | "Fin" |
| origCity | | if city after parsed not same as original data source, save original city to this field | "Galway-Cavendish and Harvey" |
| origCmty | | if cmty after parsed not same as original data source, save original cmty to this field | "Downsview-Roding-CFB" |
| origId | | if this prop have merged with other properties, save prop _id in this field | ["TRBW859491"] |
| origLp | | if lp after parsed not same as original data source, save original lp to this field | 1600 |
| origOffD | | if offD < onD, save offD to this field, then parse offD by another way | 20170428 |
| origOnD | | if onD > ts, save onD to this field, then use ts as onD | 20021125 |
| origProv | | if prov after parsed not same as original data source, save original prov to this field | "Ontario" |
| origRegion | | if region after parsed not same as original data source, save original region to this field | "York" |
| origSldd | | if sldd after parsed not same as original data source, save original sldd to this field | 20160516 |
| origSrc | | if this prop have merged with other properties, save src in this field | ["TRB"]
| origSt | | if st after parsed not same as original data source, save original st to this field | "Dundas" |
| origStNum | | if stNum after parsed not same as original data source, save original stNum to this field | "50A" |
| origTs | realmaster batch 20220204fixPropertiesOnD | | ISODate("1960-04-28T16:00:00.000Z") |
| origUnt | | if unt after parsed not same as original data source, save original unt to this field | "203E" |
| orig_lp_cd | TREB | | "Gross Lease" |
| osm | | | 1 |
| oth_struc1_out | TREB | | "Garden Shed" |
| oth_struc2_out | TREB | | "Garden Shed" |
| out_storg | TREB | | "Y" |
| outof_area | TREB | | "St. Vincent" |
| owner | | | 111 |
| parcel_id | TREB | | 105180637 |
| park_chgs | TREB | | 50 |
| park_desig | TREB | | "Exclusive" |
| park_desig_2 | TREB | | "Owned" |
| park_fac | TREB | | "Undergrnd" |
| park_lgl_desc1 | TREB | | "Level B#40" |
| park_lgl_desc2 | TREB | | "Levelc Unit 65" |
| park_spc1 | TREB | | "Ads" |
| park_spc2 | TREB | | "C65" |
| park_spcs | TREB | | 2 |
| pay_freq | TREB | | "Monthly" |
| pay_meth | TREB | | "Cheque" |
| pc | realmaster | new price - old price | -1000 |
| pcPct | realmaster | pc / old price | -0.10121457489878542 |
| pctd | TREB | | "11/10/2017 12:14:06 PM" |
| pcts | realmaster | price change change ts | ISODate("2017-11-10T18:04:43.338Z") |
| perm_adv | TREB | | "Y" |
| pets | TREB: `Pets`, RHB: `PetsAllowed` | | "Restrict" |
| pho | TREB: `pic_num` or `picNum`, DDF: `picNum`, BRE: `PhotoCount`, RHB: `PhotosCount` | | 40 |
| phodl | TREB, DDF, BRE  | photo download time | ISODate("2018-10-17T14:59:51Z") |
| phomt | TREB, DDF, BRE | photo update time | ISODate("2018-10-17T14:59:51Z") |
| phosrc | | | "trb" |
| photonumbers | | | [1,2,3,4,5,6,7,8,9] |
| pic | | | { "l": [ "https://f.realmaster.cn/P/GHJF/R.jpg", "https://f.realmaster.cn/P/GHJF/S.jpg" ] } |
| picDdf | | | { "picNum": 34, "maxPicSz": 403050, "avgPicSz": 198970.67 } |
| picTrb | | | { "picNum": 40, "maxPicSz": 28793, "avgPicSz": 16027.65 } |
| picTs | | | 20140122 |
| picUrl | BRE: compute by `phoUrls` | | "http://cdnparap130.paragonrels.com/ParagonImages/Property/p13/BCRES/261733908/[pic#]/[$width]/[$high]/6c300c027ea535a06707067e0b812cbb/16/f06790705f9a7af8659e96d07be215e8/261733908.JPG" |
| pinnum | TREB | | 581110060 |
| pix | TREB | | "Y" |
| pix_ts | TREB | | 20180419.202418 |
| pool | TREB: `Pool`, DDF: `PoolType`, RHB: `PoolFeatures` | | "None" |
| portion_property_lease_srch | TREB | | "AB." |
| poss_date | TREB | | 20220926 |
| potl | TREB | | "Y" |
| pr_lp_dol | TREB | | 988000 |
| pr_lsc | TREB | | "New" |
| pred_his | | | [ { "ts": "2019-03-28T16:45:21.274Z", "sp": 308600, "std": 9700 } ] |
| pred_mt | | | ISODate("2019-04-23T22:46:58.094Z") |
| pred_sp | | | 308600 |
| pred_spsd | | | 9700 |
| prkg_inc | TREB | Parking Included | "Y" |
| project | BRE: `ComplexSubdivisionName` or `CmplxSubd` | | "FIRST SHAUGHNESSY" |
| prop_mgmt | TREB | | "Ph Property Managenent &Consulting Inc" |
| prop_type | TREB | | "Office" |
| prov | TREB: `County`, DDF: `Address.Province`, BRE: `Province`, RHB: `StateOrProvince` | province | "ON" |
| psn | TREB: `Occ`, RHB: `Possession` | possession | "Immediatelly" |
| pstyl | TREB: `style` or `bus_type`, BRE: `StyleOfHome` | Building Style | "2-Storey" |
| ptnLsCmt | TREB: `Portion_lease_comments` | | "One Room Only" |
| ptnLsOut | TREB `Portion_property_lease1_out` + ... | | ["Entire Property"] |
| ptnLsSrch | TREB: `Portion_property_lease_srch` | | "AA." |
| ptp | TREB: `Type_own1_out` | Building Type | "Att/Row/Twnhouse" |
| ptype | TREB: compute by `ptp`, DDF: compute by `PropertyType` and `Building.Type`, RHB: compute by `PropertyType` | | "o" |
| ptype2 | TREB: compute by `ptp`, DDF: compute by `PropertyType` and `Building.Type`, RHB compute by `PropertyType` and `PropertySubType`  | | [ "Land" ] |
| pvt_ent | TREB | Private Entrence | "N" |
| rail | TREB | | "N" |
| ref_req | | | "Y" |
| region | TREB: `Area`, BRE: `Region` | | "Halton" |
| retail_a | TREB | | 2800 |
| retail_ac | TREB | | "Sq Ft" |
| retirement | TRBE | | "N" |
| rhbtp | RHB: `FeedTypes`| | [ "idx", "vow" ] |
| rltr | TREB: `Rltr`, DDF: `AgentDetails.Office.Name`, or from la | realtor name | "SHAW REALTY GROUP INC." |
| rmBltYr | realmaster | | 2016 |
| rmPsn | realmaster | estimate possession | ["inn"] |
| rmPsnDate | realmaster | estimate possession date | 20171101 |
| rmSqft | realmaster | estimate space | 1600 |
| rmSqft1 | realmaster | estimate space range lower bound | 1600 |
| rmSqft2 | realmaster | estimate space range upper bound | 1800 |
| rmStoreys | realmaster | estimate storeys | 44 |
| rmlog | realmaster | removed old edm logs | [ { "k" : "m_zh", "uid" : ObjectId("5f42f949e6f8592ba09c9c3e"), "v" : "完美无瑕，独立入口的2-Bed地下室公寓，主楼有硬木和陶瓷，环形楼梯，天窗，双门入口等。2个冰箱，2个炉子，洗衣机，烘干机，电子灯具（门除外），主楼有窗套，报警系统，中央空调，中央真空，车库开门器带遥控器，不包括：水过滤器，地下室公寓没有改造状态。" } ] |
| rmmt | realmaster | internal ts change, including change addr, loc | ISODate("2022-08-15T15:40:29.158Z") |
| rms | TREB: `Rm1_dc1_out` +	`Rm1_len` +	`Rm1_out` +	`Rm1_wth` + ..., DDF: `Building.Building` | Rooms | [ { "t": "Living", "l": "Ground", "w": 3.5, "h": 5, "d": [ "Broadloom" ] }, { "t": "Kitchen", "l": "Ground", "w": 3.05, "h": 4.11, "d": [ "Ceramic Floor" ] }] |
| rooms_plus | TREB | | 2 |
| saletp | TREB: `S_r`, DDF: `TransactionType`, BRE: `SaleRent`, RHB: `RAHB_LeaseAgreementYN`, may be overwrite by impFormat.checkPropPrice base on list price | sale typ "Lease" or "Sale" | ["Lease"] |
| scdt | TREB | | "5/17/2018 2:55:46 PM" |
| schools | realmaster: `propertyTagHelper` | schools tag | [{ "_id": 1020210, "nm": "St. Ignatius Of Loyola Secondary School", "ele": 0, "mid": 0, "hgh": 1, "gt": 12, "gf": 9, "c": 1, "fir": 7.9, "g9r": 30, "dist": 3867 }, { "_id": 1020305, "nm": "Palermo Public School", "ele": 1, "mid": 1, "hgh": 0, "gt": 8, "gf": -1, "c": 0, "fir": 6.8, "g6r": 660, "g3r": 592, "dist": 275 }] |
| schs | | schools | [1020210,1020305,1021780,1021825] |
| sd | TREB | | "10/10/2016" |
| seats | TREB | | 179 |
| sec_dep | | | "Y" |
| secgrd_sys | TREB | | "System" |
| seller | TREB | | "Betsy Mesquita And Nelson Mesquita" |
| sewage_srch | TREB | | |
| sewer | TREB | | "Sewers" |
| share_perc | TREB | | 0 |
| shoreline_exp | TREB | | "N" |
| shoreline_srch | TREB | | "." |
| showAddr | realmaster `propAddress` | | "1 King St W" |
| shr | | | 5 |
| shrappc | | | |
| shrappr | | | |
| shrc | | | |
| shrd | | | |
| shrp | | | |
| shrpappc | | | |
| shrpappr | | | |
| shrpc | | | |
| shrpd | | | |
| shrpr | | | |
| shrr | | | |
| sid | TREB: `Ml_num`, DDF: `ListingID`, BRE: `ML`, RHB: `ListingId` | | "C5481588" |
| sldDifPct | realmaster | (sp - price) * 100 / price | 4.352363146223749 |
| sldDom | realmaster: `propertyTagHelper` | sold dome | 0 |
| sldd | RHB: `CloseDate`, realmaster: `impFormat.setImportDate` | sold date | 20200102 |]
| soil_test | TREB | | "N" |
| solddate | TREB (deprecated), BRE | | ISODate("2006-10-25T16:00:00.000Z") |
| sp | TREB: `Sp_dol`, RHB: `ClosePrice` | | 2400000 |
| spcts | realmaster | price or status change timestamp | ISODate("2019-01-02T05:00:00Z") |
| spec | TREB: `spec_des1_out` + ... | | ["Unknown"] |
| spis | TREB | | "N" |
| sprinklers | TREB | | "Y" |
| sqft | TREB: `Sqft`, BRE:`FloorAreaGrandTotal`, RHB: `LivingArea` | prop space | 1300 or "0-499" |
| sqft1 | | prop estimate space range lower bound | 1300 |
| sqft2 | | prop estimate space range upper bound | 1300 |
| sqftQ | | sqft confidence level | 1 |
| sqftSrc | | | "self" or "unit" or "ending" |
| sqft_source | | | "Owner" |
| src | | property source | "BRE" or "DDF" or "RM" or "TRB" |
| srchst_num | TREB | | 2359 |
| srltr | TREB | | "ROYAL SERVICE REAL ESTATE" |
| st | TREB: `St`, RHB: `StreetName`, realmaster `propAddress` may overwrite it | street name | "King St W" |
| st_dir | TREB: `St_dir`, RHB: `StreetDirSuffix` may overwrite it | street direction | "W" |
| st_num | TREB: `St_num`, RHB: `StreetNumber`, realmaster `propAddress` may overwrite it | street number | "1" |
| st_sfx | TREB: `St_sfxm`, RHB: `StreetSuffix`, realmaster `propAddress` may overwrite it | street suffix abbreviation | "St" |
| status | realmaster | A = available, U = unavailable | "U" |
| status_cert | TREB | | "N" |
| stno | | | 2359 |
| style_attach | DDF: `Building.ConstructionStyleAttachment` | | "Detached" |
| style_split | DDF: `Building.ConstructionStyleSplitLevel` | | "Split level" |
| subCity | | BRE: `Area` | "Sunshine Coast" |
| survey | TREB | | "N" |
| synced | TREB | | ISODate("2022-04-13T14:39:55.000Z") |
| tagMt | realmaster | tags last update time | ISODate("2021-12-10T17:11:32.722Z") |
| tax | TREB: `Taxes`, RHB: `TaxAnnualAmount` | | 1771 |
| taxyr | TREB: `Yr`, RHB: `TaxYear` | | 2006 |
| tbdrms | TREB: `br` + `br_plus`, DDF: `Building.BedroomsTotal`, BRE: `TotalBedrooms` or `TotBr`, RHB: `BedroomsTotal` | total bedrooms | 3 |
| td | TREB | | "2/10/2021" |
| terms | TREB | | 60 |
| tgr | TREB: `tot_park_spcs`, RHB: `ParkingTotal` | total garage | 1 |
| timestamp | | | 20180927.104554 |
| topTs | | | ISODate("2021-09-13T17:13:41.159Z") |
| topuids | | | ["5491e3b669121a2963ae2a29"] |
| topup_pts | | | 1808 |
| tot_area | TREB | | 421 |
| tot_areacd | TREB | | "Sq Ft" |
| tot_exp | TREB | | 0 |
| tot_park_spcs | TREB | deprecated, use tgr instead, total garage | 1 |
| tpdwel | BRE: `Typedwel` or `TypeOfDwelling` or `DwellingClassification` | | "House/Single Family" |
| trbbr | | | "br" |
| trbml | | | "m" |
| trbtp | | | ["br"] |
| trbtp_addback | | | ISODate("2017-01-08T16:16:27.675Z") |
| treb | | | true |
| trms | TREB: `Rms` | total rooms | 0 |
| trnst | realmaster | transit tags | [ { "tp": "sub", "_id": "ON_TTC_14472", "nm": "Jane", "dist": 393 } ] |
| ts | TREB, DDF | prop create timestamp | ISODate("2022-10-14T22:51:45.286Z") |
| tv | TREB: `cable` | | "Y" |
| type_own_srch | TREB | | "Q." |
| type_taxes | TREB | | "Annual" |
| uaddr | realmaster: `helpers_string.unifyAddress` | | "CA:ON:MISSISSAUGA:1050 STAINTON DR" |
| uffi | TREB | | "No" |
| unavail_dt | TREB | unavaliable date | 20180925 |
| unit_num | TREB | | 109 |
| unt | TREB: `Apt_num`, RHB: `UnitNumber`, realmaster `propAddress` may overwrite it | condo unit number | "223" |
| untStorey | realmaster `propSqft.computeUntStorey` | | 4 |
| util_cable | TREB | | "A" |
| util_sewr | TREB | | "Y" |
| util_tel | TREB | | "Y" |
| utilities | TREB | | "Y" |
| vac | TREB: `Central_vac` | | "Y" |
| vc | realmaster | prop click statistics | 1736 |
| vcapp | realmaster | prop click statistics | 1620 |
| vcappc | realmaster | prop click statistics | 1551 |
| vcappr | realmaster | prop click statistics | 105 |
| vcbrowser | realmaster | prop click statistics | 4 |
| vcd | | | 1736 |
| vcmobile | | | 2 |
| vcrm | | | 1 |
| vcwechat | | | 37 |
| vend_pis | TREB | | "N" |
| volts | TREB | | 110 |
| vow | TREB | | "x" |
| vtour_updt | TREB | | ISODate("2011-02-01T20:10:25.000Z") |
| vturl | TREB: `tour_url`, DDF: `AlternateURL.VideoLink`, BRE: `VirtualTourURL`, RHB: `VirtualTourURLUnbranded` or `VirtualTourURLBranded` | | "https://tours.digenovamedia.ca/1050-stainton-drive-mississauga-on-l5c-2t7?branded=1" |
| water | TREB: `Water`, RHB: `WaterSource` | | "Municipal" |
| water_exp | TREB | | 600 |
| water_inc | TREB | water included | "Y" |
| water_type | TREB | | "Lake" |
| wtr_suptyp | TREB | | "Cistern" |
| xdtd | TREB | | "4/18/2018 12:29:52 PM" |
| zip | TREB: `Zip`, BCRE: `PostalCode`, RHB: `PostalCode` | | "L5C2T7" |
| zoning | TREB, BRE, RAHB | | "Residential" |
