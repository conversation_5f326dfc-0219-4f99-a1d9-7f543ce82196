# 需求 [fix_idx]

## 反馈

1. Tao反馈********没有更新，应该下市了

## 需求提出人:   fred

## 修改人：      rain

## 提出日期:     2024-06-07

## 原因
- rni数据库数据status = active
- 通过直接调用trebevow无法获取数据
- 通过treb online网页查看历史3-18下市
- rni的原始数据在mls_treb_idx_records 里存在，在mls_treb_idx_ids无id记录
- prop.lSrc == idx, but prop.src=['evow'], should = ['evow','idx']
- [addLog_watchImport] 定义的 _id 有问题，需要去掉srcType，push 修改记录到logs下


## 结论
1. 数据不在evow里，可能为treb evow bug
2. 数据如果不在mls_treb_idx_ids，status应该为U, 可能是import程序bug

## 解决办法

1. 手动导入
2. 排查修复idx 的bug, 从ids到records status同步问题；和prop的lSrc和src问题
3. 每周定期跑reImport prop查找>40天未更新房源，重新同步prop status数据（rni <-> properties）
4. 解决办法是有个constant的中央定义文件，把价格名称做一个array。这里遍历。
LOG_FIELDS = {} # global
impMappingTreb文件…：LOG_FIELDS.Lp_dol = 1
使用：
for field,value in LOG_FIELDS
  log[field] = prop[field] if prop[field]
另外，作为log不是检索用，尽量保存原始字段名。
5. 最好命名与其它一致, collPropertyImportLog. tmp定义全局变量比如 DB_NAME_TMP。ProcessStatusCol其实也应该改。


## 确认日期 2024-06-07
