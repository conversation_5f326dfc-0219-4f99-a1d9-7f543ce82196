## 反馈
app初始设置页面，逻辑优化

## 修改内容
1. 根据设计稿修改app初始设置，请求定位/通知权限页面(DropBox/XiAn/20200513 登录页面/初始化Setup页面@3x.png)
2. 删除页面4，内容合并至页面1，ios第二步删除，安卓保留
   1. 权限请求页面添加toggle btn
   2. 授权逻辑
    a）denied，执行完系统授权alert后，弹出等待3s，再检查状态。根据状态显示on/off状态
    b）blocked，直接go to settings进入系统设置页面。app切回前台后调用回调函数。对于低于6.3.1版本的，点on/off后显示refresh强制读取状态。
    c）点complete，不再调用授权程序，如果非granted只弹出alert（Later，Cancel）
3. 初次打开不跳转登录页面，做ab test，记录是否是登录页面，统计注册后有状态的账号，实际上注册成功的账号占比(需要单独建立branch做)
4. 在多个页面加入通知提示（具体页面待确定，暂时不做）

## 需求提出人:    allen
## 修改人:       liurui

## 提出日期
1. 2024-03-04 提出修改内容1 [修改内容1](#修改内容)
2. 2024-03-13 提出修改内容2，3[修改内容2,3](#修改内容)
3. 2024-04-16 提出修改内容2.1,2.2,4[修改内容2.1,2.2,4](#修改内容)

## 原因
1. 样式，逻辑优化

## 解决办法
根据修改内容修改
1. 修改样式，分步骤确认权限
2. 根据手机系统判断步骤数量，根据是否授权判断显示
3. ab test具体做法@fred@rain

## 需求确认日期
1. 2024-04-17


local storage的存储与url域名绑定，在国内外切换域名后无法正确存取只和app相关的全局状态值。所以引入app storage。
因为app storage存储保存时6.5前老版本有3s delay（删除时没有delay），所以同时使用local storage。

App storage和local storage新添加储存数据：
1. 页面进度curStep 1,2,3（判断setup页面是否执行完,setup流程结束会删除）
2. 首次执行initApp 1-完成，0-未完成
3. request的通知设置结果notificationPermission，1-已经调用过系统授权alert
4. request的定位设置结果locationPermission，1-已经调用过系统授权alert

赋值方式
系统权限结果：
1.在setup的对应step页面，init时(on/off都不选时)，调用request，存储当前类型permission至app和local storage中。
2.因为是新加字段，兼容性考虑判断已经init过的情况，强行赋值=1

页面进度：
只有进入setup的每个step页面时存储步骤

首次执行：
1.在打开setup时，检查首次执行=null，保存新结果保存为0，结束setup后保存为1。
2.因为是新加字段，兼容性考虑判断已经init过的情况，强行赋值=1


home页面

//苹果不存在老版本重装的问题
if 当前版本<6.5 & =ios
设置首次执行=1
设置通知设置结果=1
设置定位设置结果=1

//安卓老版本没有重装时执行以下程序
if 当前版本<6.5 & =安卓 & cookie有语言设置
设置首次执行=1
设置通知设置结果=1 //即使从未request也不影响通知设置的授权状态判断
设置定位设置结果=1


if 页面进度不为null
弹出setup页面

if 首次执行=null
弹出setup页面



setup页面

页面初始化
1. 判断是否为首次执行
2. 跳转到相应的步骤

permission.check逻辑
ios：check结果就是系统当前的权限，所以不用处理，直接根据check结果显示授权状态。

安卓：在check时，如果是granted，直接显示授权状态；如果是denied（安卓check接口不会返回blocked状态，只会返回denied状态），执行以下逻辑为了区分blocked状态：

//安卓默认给了通知授权，对于request是否被执行过的判断没有作用了，所以强制返回blocked的状态
1. if 当前系统=安卓 & 当前在处理通知设置
    通知设置状态=blocked，设置off状态，退出逻辑

//只针对位置授权设置有效
2. 调用getItemObj获取app保存的授权结果
   a. 返回值为null从未执行request过（即从未打开过系统授权alert），则认为是init状态，on/off都是不选状态，退出逻辑
   b. 有返回值，此时的授权状态=blocked，设置off状态，退出逻辑
