# 需求 [fix_similar_link_prop]

## 反馈

1. tao反馈N8254734查找相似房源报错(因为没有处理Link房源)
2. fred提出除Link以外没有其他满足条件的ptype2，就将Link认为是Detached去查找相似房源，并且将Link作为加/减分项增加权重
<!-- 目前相似房源筛选支持的ptype2类型['Detached','Semi-Detached','Townhouse','Apartment','Bungalow'] -->

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2024-04-23

## 原因

1. 目前相似房源查找不支持ptype2为Link的类型

## 解决办法

1. 如果房源ptype2除Link以外没有其他类型满足相似房源查找,将Link认为是Detached查找相似房源。
2. 房源ptype2如果含有Link类型和另外一个可以用来filter的类型时,在查找相似房源时需要将ptype2中的Link作为加/减分项计算权重(10分)得到最终得分。
<!-- ptype2:['House','Link',''Semi-Detached']时,使用'Semi-Detached'筛选房源,但是'Link'会作为权重计算 -->

## 确认日期:     2024-04-24
