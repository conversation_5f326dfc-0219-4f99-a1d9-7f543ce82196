# 需求 [fix_propCity]

## 反馈

1. Tao反馈出现大量重复房源问题
2. Rain在执行完fix_propMerged batch之后发现还存在没有merge的房源
3. Rain反馈fix_propMerged batch会导致RM房源merge bug

## 需求提出人:   Rain

## 修改人：      luoxiaowei

## 提出日期:     2024-07-16

## 原因

1. 房源存在city为Toronto C07的case，导致uaddr错误 没有merge
2. 反馈2是因为DDF房源判断Board为82, 85, 86时,把sid加上'TRB'查找房源，否则使用uaddr,sid,onD等信息查找，因为Board不在范围内，uaddr不一致导致没有找到merge房源
3. 反馈3是因为RM房源没有uaddr，进入merge逻辑是没有处理

## 解决方案

1. formatProvAndCity添加对city为Toronto C07 cases的特殊处理(和Fred确认过只有Toronto会出现)，添加unit test
2. DDF房源根据Board查找TREB房源的逻辑改为判断ontario省的DDF房源通过sid去查找TREB房源进行merge,添加unit test
3. 添加batch修改这种case房源的city,origCity,uaddr
4. 房源merge添加src === RM 的判断，不进行merge房源查找

## 确认日期:      2024-07-17

## online-step

1. 重启watch&import
2. 执行batch->fix_propCity.coffee 修复city
3. 执行batch->fix_propMerged.coffee 重新merge
