## 反馈
1. fred @彭鑫(公亮) @Rain 对于未登录的user（App/Web）的行为统计和记录，是否注册，包括今后的A/B test，我们需要有个整体策略。现在的pn collection应该满足不了需求。是用device id，session id，或者其它什么id来定义key？这个coll是否包括已经注册用户？可能这两天要确定一下。@罗晓伟 @杨安旭 @biubiubiu 的有些程序修改可能已经需要这个部分了。
2. native存在bug，接口无法返回unique_id，需要更新native(只是接口无法返回，userAgent中存在)



## 需求提出人:   rain
## 修改人:       liurui

## 提出日期
1. 2024-04-23提出反馈1

## 原因
1. userAgent 存在RMuserID字段,是通过DeviceInfo.getUniqueId()获取的，
2. 但是旧版本app有bug，无法通过接口获取，只能先读取用户的userAgent来获取
3. 如果db没有存userAgent，那么可以在用户登录/session更新记录此RMuserID

# Sample Data
n&id=TRBN7297554&mode=list
+2024-04-23T21:50:46.971 ************** POST realmaster.com/1.5/props/detail 1713923446971 Mozilla/5.0 (iPhone; CPU iPhone OS 17_4_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148; RMuserID/E1C25864-4CE5-44CF-81F2-4158D75BF58A https://realmaster.com/1.5/prop/detail/inapp?lang=zh-cn&id=TRBN7297554&mode=list
+2024-04-23T21:50:46.972 *************** POST realmaster.com/1.5/props/search 1713923446972 Mozilla/5.0 (iPhone; CPU iPhone OS 15_6_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148; RMuserID/e41b6b9561c54ed8

## 解决办法
<!-- 登陆用户在更新key的时候记录/更新RMuserID -->
1. 参考collection->device_id确认是否可以使用该表记录RMuserID/userID/pn。
   1. 如果可以使用，在该表基础上添加字段,建立索引
   2. 如果不能使用，新建一张表用于保存RMuserID/userID/pn信息。
   3. 表记录设置有效期(暂定半年后自动删除记录)
2. 确认现在是否存有userAgent数据
   1. 如果有存，添加batch对userAgent数据解析出RMuserID进行保存<!-- device_id存有ua,cookie等信息,不是每个用户都会生成 -->
   2. 如果没有，不做处理，在用户访问页面时保存/更新RMuserID等数据
3. 未登陆用户需要添加访问记录<!-- 未登陆用户需要添加访问记录,暂时不考虑 -->
4. 确认网站端是否可以获取RMuserID信息。<!-- web端获取不到RMuserId,和rain/liurui确认是否可以得到,考虑使用机器码? -->
5. 确认分享获取uniqueID问题(主要微信是否可以得到uniqueId)<!-- 和web端一致，需要确认。分享页面获取不到RMuserId,不确认是否native没有获取 -->



## 需求确认日期   2024-04-22

## 2024-05-14 新问题与解决方案变更

1. deviceId不能用于唯一值去添加/更新用户的uuid
2. 一个设备会登陆多个账号(或者一个用户多个deviceId), 所以deiviceId不能用于唯一key去生成uuid
3. 同一个用户在web端可能会在多个浏览器登陆
4. 用户已经有账号,没有登陆的情况下生成uuid, 登陆之后又产生新的uuid 怎么将两个uuid关联起来?
5. web端登出 cookie中旧的uuid需要替换成一个新的uuid
6. fred提出一个解决方案 用户存在多个uuid
   1. 用户uuid需要三个fields，唯一的uuid，deviceId，b4luuid(beforeLoginUUID,类型为array,使用addToSet更新,需要限制sizeLimit)
   2. 需要有一个新collection来保存所有的uuid,用于关联登陆与未登陆产生的uuid
   3. 整体类似push token，不过是从后端传给前端

## 2024-07-01 确认修改方式
<!--        collection - data.user_uuid
   _id:        string      uuid 由后端生成的12位nanoid
   RMuserID:   string      RMuserID 存在则保存
   uid:        objectId    user._id
   ua:         string      添加记录时的userAgent
   ts:         date        create time
   _mt:        date        update time
-->
<!--        collection - data.user 新加字段
   uuid:       string      注册/第一次绑定时的uuid
   b4luuid:    array       每次登录时cookie中的uuid
-->
1. session添加fn生成uuid，保存至user_uuid
2. 登录时保存uuid至coll user，更新uuid和b4luuid字段
3. 登出时删除cookie中的uuid

model user
   bindUid 如果user有uuid，只更新b4luuid，没有uuid时两个都更新,返回user coll保存的uuid

online step
   config.coffee文件在preDefColls下面添加uuid coll的信息
   uuid:
      dbName:'chome'
      collName:'user_uuid'