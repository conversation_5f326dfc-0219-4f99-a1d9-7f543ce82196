# 需求 [fix_cn]

## 反馈

1. 隐藏cn网站的话题频道
.cn网站，目前国内访问隐藏话题菜单，连vpn后可以看到话题菜单。考虑彻底隐藏。这个可以跟当前网站收藏改版一起改下。
.cn/forum（网站）和.cn/1.5/topics/（微信分享），都打开的是新闻帖子。

  判断如果是个人发帖非房大师发帖（类似判断参见app新闻频道的个人帖tab），跳转网站出错页面（跟wpnews类似）。目的是block所有个人发布的新闻帖子。(管理员除外)


2. 微图文，可以改也可暂时不改 （可以通过参数控制.cn的为图文是不是只在微信中打开）

微图文分享到微信中会有两个.cn域名的link（直接分享微图文，从分享的微网站打开的微图文，rain了解更多细节）

因为有些微图文可能涉及侵权，所以为了保护.cn域名考虑采取如下措施：

对于.cn结尾的微图文link只在微信中可以打开，在网页中无法打开，返回类似打开下面link类似的错误提示：
https://www.realmaster.cn/wpnews/613ae2b0c40fe257107308df


昨天上面的url给删除了，并且rain把wpnews这个api给取消了（已经不需要再使用）。取消wpnews前，访问上面的删除的网址，返回一个网站的出错页面（类似url过期的Oops页面）；取消wpnews后，现在返回的是App的报错页面。

我的需求是仍然返回网站报错的页面，而非app报错的页面。如果需要你可以跟Rain沟通。

说明：该策略只针对.cn域名，.com域名不受影响。微信的分享接口只接受备案域名，因此只能用.cn域名。

当然这样可能会在某些场景下使用不方便。

 .cn/wecard/prop（微网站打开微图文）和.cn/s/（微图文微信分享）路径可能是微图文路径。禁止后跳转网站出错页面。

  这个可以统一搞几个访问权限配置开关：
  1. 非微信打开时=国内IP禁止
  2. 微信打开时=不限制



3. wpnews路径取消后的报错改成网站报错。目前网站报错页面好像比较混乱，很多报错是app样式的而非网站的，可能网站和app共用的原因，可以理顺下原理，如果不好改暂时不改也可以，或者区分app和网站分别采用不同的通用报错页面。


## 需求提出人:    Allen

## 修改人：       liurui

## 提出日期:      2025-05-15

## 原因

1. 以前判断的ip和语言
2. 以前未要求

## 解决办法

1. 话题访问限制方案：
   1. 网站端菜单隐藏：
    在网站导航菜单渲染时，增加域名判断
    如果是.cn域名，不显示话题频道菜单项
    修改：
      在 appweb/src/apps/80_sites/01_lib/11_pageDataMethods.coffee，第112行，添加hostname相关判断
   2. 话题访问限制：
    对于.cn/forum和.cn/1.5/topics/路径的访问
    从数据库获取帖子数据后，检查数据中的fornm字段
    如果存在fornm字段（个人发帖），返回网站错误页面（webErrPage）
    如果不存在fornm字段（房大师发帖），允许正常访问
    修改：
      .cn/forum 在appweb/src/apps/80_sites/WebRM/forumServer.coffee文件下修改
      .cn/1.5/topics 在appweb/src/apps/80_sites/AppRM/forum/forum.coffee的 getForumDetailPage2 中修改

2. 微图文访问限制方案：
   1. 访问控制：
      添加global参数控制限制是否生效
      当限制生效时：
        使用req.isWeChat()判断是否在微信中打开
        使用req.isChinaIP()判断是否是中国IP
        对于.cn域名的微图文链接：
          非微信打开时，
            是中国IP,返回网站错误页面（webErrPage）
            非是中国IP，允许正常访问
          微信打开时，允许正常访问
      当限制不生效时：
        允许正常访问
    修改：
      global的判断参数和逻辑判断可以设置在 appweb/src/model/wecard.coffee 文件 class Wecard 下
      wecard/ 路径下的相关内容在 appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee 文件中的 process_wecard_req 修改
      /s/路径下的相关内容在 appweb/src/apps/80_sites/AppRM/shortUrl/shortUrl.coffee文件的第26行修改
3. 错误页面处理方案：
  web端统一使用webErrPage作为网站错误页面，不再使用App的错误页面（generalError）
  App的错误页面（generalError）添加处理，如果是从浏览器进来的，显示webErrPage样式



## 影响范围
web端

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-20

## online-step

1. 重启server
