# 需求 [app_token]

  增加Token新功能
  1. 用户查看页面: 用户可以看到累计点数，历史加分/减分记录（用户不可操作加分减分）,修改的管理员姓名，每个月的balance。tokenAdmin可以点击姓名进入wecard页面。
  2. 管理员查看用户列表：该页面可根据email模糊搜索用户，默认出现最近操作过的用户,每页20条。点击Modify Token按钮进入该用户的Token编辑页面。点击用户进入用户历史Token详情列表。
  3. 管理员修改用户Token页面： 1.未操作加分/减分时默认灰色调。填写理由的输入栏，默认灰色调。2.输入加减分时，分割线绿色#5EB85C提示，文字颜色变#000000，加减分分割线下提示最新分数。填写理由的输入栏边框也是绿色#5EB85C（必填）。
  4. 管理员查看用户历史Token细节页面：与用户查看页面类似。但是可点击进入Token编辑页面。
  5. Token项目分数管理界面：管理员使用，可以修改系统给定的token项目分数
  6. 提前在token表里填入一批用户，指定的用户才有Token。
  管理员：tokenAdmin：_admin,_tokenAdmin
  用户查看的入口在me -> edit Profile区域块内；Token项目分数管理界面，在more。

  2024.7.22 提出新反馈，增加多种类型Token，各种类型Token之间余额，加减分数等互不相关。

  1. 使用一张token表，表结构参照token Collection，增加tp字段表示该条记录是关于哪一个token类型的；

  2. Me界面Token余额显示：将有记录的Token类型的余额显示在Me页面上，逗号分割余额；

  3. 历史记录显示页面：增加类型筛选，可以选择查看哪个类型的Token历史记录；将选择内容用tag显示在页面最上方，每个tag上面显示余额；

  4. Token System Manage页面：保持现状不变，增加type的显示，用一个记录，在原先的表基础上给每一个item增加tp字段，表示当前项目用于哪一个token类型的加减分；后续用户在点击某个操作触发token加减分，用来给token表的记录填写tp字段数据；

  5. Token Management页面：增加token类型选择（会有默认选择的类型），选择哪个类型后显示该类型下存在token记录的用户信息；邮箱搜索基于token类型选择；

  6. 管理员编辑用户token值页面：增加token类型选择。从上一个页面选择的token类型会做为该页面token类型的默认选项；修改积分针对该类型tokne进行修改。


## 需求提出人:   fred

## 修改人：      zhangman

## 提出日期:     2024-06-17

## 解决办法

数据结构和api设计参考 docs/Dev_token/token.md 文档

## 确认日期:    
2024-06-21
2024-07-22
