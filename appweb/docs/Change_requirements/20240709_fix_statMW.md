# 需求 [fix_statMW]

## 反馈

1. Tao反馈市场行情在市房源数量统计值错误,edm邮件房源统计错误
2. <PERSON>反馈市场行情右边余白问题需要调整

## 需求提出人:   Fred

## 修改人：      luoxiaowei

## 提出日期:     2024-07-07

## 原因

1. 反馈1是因为在统计时没有过滤掉merged房源，并且存在一些房源状态一直没有更新

## 出售房源查找结果

1. 使用原有query在测试服统计:23003,语句:{city:'Toronto',prov:'ON',ptype:'r',saletp:'Sale',onD:{$lte:20240616},$or:[{offD:null},{offD:{$gte:20240623}}]}
2. 添加merged过滤在测试服统计:11527,语句:{city:'Toronto',prov:'ON',ptype:'r',saletp:'Sale',merged:null,onD:{$lte:20240616},$or:[{offD:null},{offD:{$gte:20240623}}]}
3. offD不存在时onD限制在100天内在测试服统计:9524,语句:{city:'Toronto',prov:'ON',ptype:'r',saletp:'Sale',merged:null,onD:{$lte:20240616},$or:[{offD:{$gte:20240623}},{$and:[{offD:null},{onD:{$gte:20240308}}]}]}
<!-- 测试服只有2024年6月12日之前的数据 -->

## 解决办法

1. 在市房源统计时添加merged过滤,暂时不添加offD不存在时onD需要在100天内的限制
2. 修改市场行情前端显示右边数字显示不全的余白问题

## 确认日期:      2024-07-08

## online-step

1. 执行以下命令，重新做房源统计
nohup ./start.sh lib/batchBase.coffee stats/prop_mw.coffee AllM AllW > ./logs/prop_mw.log 2>&1 &
