## 反馈 [fix_similarWTs]
1.Tao 反馈相似房源选择更久远的月份没有更多的房源进来

## 需求提出人:   Tao
## 修改人:      zhangman
## 提出日期
1. 2024-04-25

## 原因
1. 后端最终返回的数据只给10个。
2. 放宽时间后，数据库查询数据根据房源最后更新日期进行降序排序，选择了前100个房源。导致时间越远离现在的房源，还没有进行到判断是否相似的步骤就被过滤了。
3. 时间差异权重与平均权重计算衡量问题。房源最后更新日期距离当前时间越远，时间差异权重越大，导致总差异权重越大。然而，平均权重的计算是将房源列表按照总差异权重升序排序后，取最小的三个房源进行计算。导致最后更新日期距离当前时间远的房源总权重高于平均权重而被过滤掉。

## 解决办法
2024-05-07 Fred提出修改建议
1. 前端给page和limit，后端计算offset用于数据库限制查询，规定当offset达到250后就不进行数据库查找，返给前端0个房源信息。
2. 数据库查询房源信息时的sort用range升序和mt降序排列，返回查询到的房源信息的同时返回count。
3. 根据选择的月份给出不同的时间权重，将其中固定写死的数值改成根据month取值.

2024-05-09 Fred提出修改建议
1. 数据库查询limit限制500
2. 不计算平均权重，不用判断是否相似，按照权重排序后，根据前端传入的limit进行分页返回，当offset大于250时不调接口，提示没有更多数据。
3. 修改es的getSort函数支持多个条件排序

2024-05-10 Fred提出修改建议
1. 同楼房源选择房间数的tag修改，eg：将1+ 修改为1+•，查找的是1+含有额外房间数的房源

## 需求确认日期
1. 2024-05-08与Fred确认需求
2. 2024-05-10与Fred确认需求
3. 2024-05-11与Fred确认