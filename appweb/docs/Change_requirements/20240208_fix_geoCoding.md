# 需求 [fix_geoCoding]

## 反馈

1. fred反馈房源E7368328 city为Toronto但是地图定位在Markham (2024-01-16)
2. rain反馈添加/修改geo_cache时aUaddr出现duplicate key问题，需要使用transaction
3. fred反馈CA:ON:TOONTO:38 WILLIAM ST 位置(经纬度)信息错误 (2024-03-13 email反馈)

## 需求提出人:    fred,rain

## 修改人:       luoxiaowei

## 提出日期

1. 2024-01-16提出反馈1和反馈2
2. 2024-03-13通过email提出反馈3

## 需求确认日期

1. 2024-01-16 meeting确认 [反馈1和反馈2](#反馈)
2. 2024-03-18 meeting确认 [反馈3](#反馈)

## 原因

1. rni提供的房源邮编错误，导致定位到其它城市，geo q计算没有对city prov等进行判断
2. aUaddr是唯一索引，多线程修改geo_cache数据时导致
3. 历史数据位置信息计算使用osm导致CA:ON:TOONTO:38 WILLIAM ST 经纬度错误

## 解决办法

1. geocoding计算geoq时添加对cnty，prov，city(subcity)，cmty，zip判断
2. 通过geocode获取位置信息时不添加邮编(zip)
3. 添加batch找出现有geo_cache中存在位置错误(cnty,prov,city与uaddr不一致)，与通过osm获取位置的数据重新做geocode，然后执行uaddr归一batch
