# 需求 [fix_uaddr_zip3]

## 反馈

1. fred反馈在多伦多有2个同样地址的街道(CA:ON:TORONTO:120 HOMEWOOD AVE)，只是社区和zip不同,我们一开始的假设不成立了。uaddr要修改，可能需要加入zip才行。

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-05-21

## 原因

1. 一开始假设同一个城市不会存在完全一致的街道地址,所以uaddr中没有加入zip信息
2. 碰到过因为zip填写错误导致loc获取错误的问题,所以在geocode中取消了zip(参考20240208_fix_geoCoding.md)

### 解决办法

1. geocode获取loc时加入cmty,zip(func->getFullAddress) qaddr = "#{addr}, #{cmty},#{city}, #{prov}, #{zip},#{cnty}"
2. uaddr生成时加入zip前三位(unifyAddress),eg:CA:ON:TORONTO:M4Y:120 HOMEWOOD AVE <!-- geocode获取的优先级大于rni数据提供的 -->
3. 数据修改方案(部分修改)
   1. 对已经发现的同城市同街道名的数据进行修复
   2. 添加batch对geo_cache中没有zip的数据添加zip字段
   3. geocode流程修改, 现有的数据可能有同城市同街道名的数据, geocoding 新房源进来 可能重名 判断是否需要加zip，添加zip match(不match的记录数据库, warning log)   需要一个ignore的zip列表
      1. checkAndformatGeoInput 返回值改为: error, uaddrBeforeFormat, uaddrAfterFormat, uaddrZipBeforeFormat, uaddrZipAfterFormat
      2. checkGeoCache 查找有zip(uaddrZipAfterFormat)和没有zip(uaddrAfterFormat)的记录,优先返回包含zip的记录,没有zip的记录需要比对zip前三位(一起查)
      3. saveGeoCache 添加zip前三位match判断, 如果已有记录并且zip前三位不match, 新建记录并且uaddr添加zip前三位
4. 添加与修复unit test
5. 工作量预估: 5天

## 确认日期:    2024-06-21

## 预计完成时间:  2024-06-28

<!--  使用uaddr的collections:
修改前提: 需要将房源, 笔记, geo_cache等数据修改完成后再修改其它数据
先重启watch可能会导致每个房源进来都需要geocode, 可能会达到上限, geo_cache目前存在30660条记录没有zip信息
可能需要先跑完batch 再重启import的watch
   1. vow->amenities_aggregate      _id                     可以直接修改
   2. vow->condos                   _id                     可以直接修改
   3. vow->condos_del               _id                     可以直接修改
   4. vow->condos_logs              _id                     可以直接修改
   5. vow->condos_sqft_queue        uaddr                   可以直接修改
   6. vow->coophouses               _id uaddr               可以直接修改
   7. vow->floorplans               uaddr                   可以直接修改
   8. vow->geo_cache                _id  aUaddr  uaddr
   9. vow->geo_cache_bc             _id  aUaddr  uaddr
   10. vow->geoCacheOrig            _id
   11. vow->properties              uaddr
   12. vow->properties_condo        _id
   13. vow->properties_deleted      uaddr
   14. vow->props_part              uaddr
   15. vow->school                  uaddr                   可以直接修改
   16. vow->school_eqao_missing     uaddr                   可以直接修改
   17. vow->sqft_middle             uaddr                   
   18. vow->sqft_to_process         uaddr                   
   19. vow->sqfts_aggregate         _id                     
   20. vow->university              campus.uaddr            可以直接修改
   21. chome->evaluation            _id                     可以直接修改
   22. chome->listing_stats         _id                     可以直接修改, 目前好像没在使用
   23. chome->notes_del             uaddr                   可以直接修改
   24. chome->prop_notes            uaddr
   25. chome->showing               props.uaddr             可以直接修改部分数据
   26. chome->user_stream           uaddr                   可以直接修改部分数据
   27. chome->user_watch_location   uaddr                   可以直接修改部分数据
-->

<!-- 涉及uaddr处理的地方(均为uaddr.split(':')获取city, addr等信息), 都改为使用addAddressToResult方法
   1. src/apps/80_sites/AppRM/propRelated/evaluation.coffee#425   POST 'histcnt'
   2. src/batch/correctData/mergeEvaluation.coffee#67             确认是否还使用
   3. src/batch/geo/fix_geoCache.coffee#73                        batch还未跑完
   4. src/batch/prop/refineGeocoding.coffee#207, 301
   5. src/libapp/condos.coffee#359                                addAddressToResult
   6. src/libapp/provCleanHelper.coffee#211                       formatGeoCacheId
   7. src/model/011_userWatch.coffee#80
-->

## online-steps

1. 重启watch&import
2. 执行batch->src/batch/geo/add_geoCache_zip.coffee (对geo_cache中没有zip的记录进行添加)
3. 执行batch->src/batch/prop/fixUaddrAndReImport.coffee (目前发现的同城市出现相同街道的cases)
