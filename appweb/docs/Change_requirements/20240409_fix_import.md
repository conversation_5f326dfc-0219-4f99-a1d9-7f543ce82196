# 需求 [fix_import]



## 反馈
1. tao/sales反馈有些房源在市状态没有更新
2. 我们需要有个自动侦查房源是否有问题的机制。比如10天以上没有更新的，自动用id查一下。之后有几种情况。有可能的确没有更新。如果返回错误，需要加到手动更新一个列表，发我们自己。如果已经更新了状态，这个可能是程序出问题了，要列出ID和详情并报警给我们。

## 需求提出人:    tao/fred
## 修改人:       luoxiaowei/rain

## 提出日期
1. 2024-03-27提出反馈1
2. 2024-03-31fred提出反馈2


## 原因
1. debug结果为房源在treb的node-rets 返回未找到，可能因为房源从evow系统撤出
2. 不确定为何房源状态在市超过10天未能更新


## 解决办法
1. +batch从properties数据库自动查找10天没更新的active房源(必须是evow)，加入到该db(collManualIDs)，增加priority优先级，自动程序可以重新按照id查找这些房源， see: src/batch/prop/reImportProp.coffee
2. batch 可以完成以下功能
   1. 找出这类房源并且和rni数据做比较，看是watch问题还是treb import问题。(和master数据对比，status,price,lsc)
   2. 在trebImport里重新按照id查找并且import这些房源，see：trebDownloadV2/collManualIDs，
   <!-- 不管有没有问题都需要从collManualIDs中删除对应记录 -->
   <!-- 有问题时通知admin,正确的数据存入数据库 -->
   <!-- 得到的房源指的是重新下载得到的房源 -->
   3. 如果得到的房源状态和rni一致，说明import没问题,id从collManualIDs中删除。如果得到的房源状态和rni不一致，说明import有问题（见2.4）。如果未找到说明从evow撤出，见3
   4. 通知管理员假如有异常
3. Evow如果撤出属于正常情况。如果是idx，查找idx状态，如果在线，改lastSrc为idx。如果idx状态如果是U，把房源状态改为下线，从collManualIDs中删除对应记录。(idx中不存在lsc字段)




## 需求确认日期
1. 2024-04-09 meeting确认 [反馈1](#反馈)

