# 需求 [fix_ohShowing]

## 反馈

1.  用户发邮件给**********************，Listing Open House time Error MLS#: N8161514：I noticed There's an error on the open house time on your app shows 24pm- 16pm  Vs on MLS it's 12pm- 4pm.

## 需求

Open House 前端显示使用12小时制，如果是24:00，则显示00:00AM；12:00 显示 12:00 PM；如果转换成12小时制后的时间小于6点钟，则不显示AM/PM，让用户自己判断

## 需求提出人:   Fred

## 修改人：      zhang man

## 提出日期:     2024-03-22

## 确认日期:     2024-03-22 微信确认

## 原因

根本原因：open house时间问题由于DDF对时间处理bug(ddfFormatDateTime)，将'12:00 PM'解析为'24:00'。
其次前端显示时间用24小时制，但是后面还加了PM/AM 12小时制用用语

## 解决办法

根本原因由罗晓伟解决，该branch解决前端显示问题
写三个处理时间的函数：
convert24HoursTo12Hours：将24小时制的时间转换成12小时制(包含处理24:60:60这种数据)
specialDealOhzTime：特殊处理ohz时间，如果小于6:00AM，可能数据不对，后面不要加AMPM，让用户自己判断
OhzArrConve24HoursTo12Hours：处理web端显示
修改web端和app端前端调用