# 需求 [merge_condos]

## 反馈

1. fred反馈condos地址没有归一处理,出现了CA:ON:TORONTO:181 DUNDAS ST E,CA:ON:TORONTO:181 DUNDAS ST

## 需求提出人:   fred

## 修改人：      luoxiaowei

## 提出日期:     2024-05-18

## 原因

1. 地址归一处理时只针对了apartment类型的房源,并且没有修改condos信息

## 调查结果

1. condos表中存在353条记录的uaddr在geo_cache中找不到
   1. error log
   2. 重新生成geo_cache
   3. 需要判断是否存在可以merge的记录
2. 共有2821条记录可以做merge

## 解决办法

1. 添加condos import的流程图
2. 查找condos表中可以地址归一的记录,进行merge
3. merge逻辑
   1. buildings数据进行合并
      1. 图片对比hash一致的只保留一条, 不一致的都保留
      2. array字段合并, 非array的字符串字段, 比较是否一致，不一致的情况下用';'拼接起来
      3. age字段取较小值，cnts/vcnts字段取和，其它number字段取较大值
   2. floorplan记录判断是否存在重复内容(img, name, unit, floor, fce), 存在重复只保留一条, 添加warning log

## 确认日期:    2025-06-14

## online-step

1. 执行batch->src/batch/condos/mergeCondos_byUaddr.coffee
