# 需求 [rmEnquiry_report]

RealMaster Enquiry 月度报告 ： 记录lead来源 按照Property State （安省的 要具体到Toronto， Peel， York， 其他省的 按照省分 ） Property Price（ 三档： 1800以下 1800-10000， 10000以上） 语言类（ 国语 粤语 英语）

## 需求提出人:    Bowen
## 修改人：       ZhangMan

## 提出日期:      2025-06-25

## 解决办法

写batch去统计需要的信息：
1. batch参数时间支持按月还是按周统计，如果type选择m，则统计数据为输入日期的上个月1号，到上个月最后一天的数据，默认是本月的日期；如果type选择w，则统计数据为输入日期的上周一到上周日的数据，默认是上周
2. 从form_input表中查找page在['mls', 'project', 'trustedAssignment', 'saleTrustedAssign', 'evaluation', 'rmListing', 'student_rental']范围内的数据进行统计
3. 统计生成的xlsx发送邮件，邮件发送成功后，删除文件

以下是和Bowen确认的表格展示样式，其中第二，三，四部分只统计mls房源
第一部分：标题是Follow Up Boss type。内容是根据PAGE_TYPE进行分类统计。展示顺序和内容是：第一列第一模块是app.mls，web.mls，app.evalucation，website，app.rmListing，web.student_rental，统计每一个分类的总和以及第一模块的总计，空一行。第二模块是app.project，email. project，app.project，Web.project，统计每一个分类的总和以及第二模块的总计，空一行。第三模块app.Assignment，app.TrustAssignment，Web.trustAssignment，统计每一个分类的总和以及第三模块的总计，其中TrustAssignment =‘trustedAssignment’+’saleTrustedAssign’；
第二部分：标题是Follow Up Boss prop type，内容是根据mls房源的类型是出售还是出租以及房源价格进行统计。顺序是Property Inquiry lease total，Property Inquiry less than $1800，Property Inquiry more than $1800，Property Inquiry sale total，Property Inquiry less than 百万以内，Property Inquiry 百万以上
第三部分： 标题是leader city。内容是根据省份和区域划分。除了ON省，BC省只按照省份统计在一起，得到一个总数。 AB省要区分CALGARY和EDMONTON城市，其余省份全部统计在OTHER里。 ON省有单独的规则： toronto， ottawa， kitchener和Waterloo，直接按城市进行统计， 其余数据需要根据id查询PropertiesCol表获取area字段，分别统计York，Peel，Durham区域，剩余的数据统计在ON省的OTHER里
第四部分： 标题是语言。内容是根据语言进行统计。 如果local字段里只有English则认为是English，如果还有其他语言，则按照其他语言优先进行统计。分为Chinese， Cantonese， English， other

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-02

## online-step

按照需要启动batch
```
sh start.sh -t batch -n rmEnquiryReport -cmd "lib/batchBase.coffee batch/form/rmEnquiryReport.coffee -t m -d 2025-07-01 -e <EMAIL> -f xxx"
```
