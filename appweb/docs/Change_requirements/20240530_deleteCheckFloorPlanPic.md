# 需求 [deleteCheckFloorPlanPic]
1. 图片管理模块删除图片时，检查condos表里的img和floorplan，如果存在则不能被删除
2. 图片管理模块删除图片时，一次最多只能选择9张
3. 写batch检查forum/wecard/userlisting/pre_constrct/condos表里，用户自己上传的图片是否保存了域名，如果保存了，删除域名。修改condos相关接口，保存时去掉域名，查看时增加域名。

## 需求提出人:   Fred

## 修改人：      zhangman

## 提出日期:     2024-05-28

## 解决办法
1. 修改checkFileReference函数增加condos表的检查，匹配时将正则查找换成直接查找。
2. 优化checkFileReference函数里每个表的查询速度：给forum，userlisting，condos表增加相关索引
3. 写batch检查forum/wecard/userlisting/pre_constrct/condos表里，用户自己上传的图片是否保存了域名，如果保存了，删除域名。
4. 修改condos和user_listing,forum,group_forum相关接口，保存时去掉域名，查看时补充域名。

## 确认日期:     2024-05-31