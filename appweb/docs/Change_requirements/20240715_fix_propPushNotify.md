# 需求 [fix_statMW]

## 反馈

1. Fred反馈用户***********************接收不到房源推送

## 需求提出人:   Fred

## 修改人：      luoxiaowei

## 提出日期:     2024-07-12

## 原因

1. 该用户存在edmNo，在生成user_log时被过滤掉导致没有保存记录，在Feeds中没有房源显示
2. propPassSearchCondition对保存有min_lp/max_lp的条件存在lease条件的判断bug

## 解决办法

1. 生成user_log时取消edmNo,edmNoSearch,pnNoSearch的过滤，改为在发邮件或者notify时再过滤
2. propPassSearchCondition根据saletp判断使用lp还是lpr比较min_lp/max_lp

## 确认日期:      2024-07-15

## online-step

1. 需要重启watchPropAndPushNotify
2. 需要监测batch时间以及是否存在效率问题，导致通知内容无法生成/发出
