# 需求 [fix_geoCoderUt]

## 反馈

1. rain反馈需要确认branch [geo-coding-fix-uaddr-improve]是否还符合现在系统功能，补充需求文档。

## 需求提出人:   rain

## 修改人：      luoxiaowei

## 提出日期:     2024-05-08

## 原因

该branch最先的目的是为了修复Apartment的geoCoding查找相似地址(fixAddrByFindSimilarAddrInGeoCacheResults)需要去掉dir,sfx的问题，
后来因为在propAddress.formatProvAndCity中已经得到解决，所以该branch可以删除。

## 结论

1. 该branch修改的需求已经在其它更新中实现，可以删除该branch
2. 需要对geoCoder的 unit test进行修复与cases添加。

## 确认日期:     2024-05-09
