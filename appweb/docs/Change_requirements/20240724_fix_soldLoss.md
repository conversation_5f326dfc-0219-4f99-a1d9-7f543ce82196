# 需求 [fix_soldLoss]

## 反馈

1. sales反馈卖亏房源的卖亏比例计算存在问题(TRBW8372604)
2. sales反馈卖亏的计算是用买价与租价对比(TRBW8225608)

## 需求提出人:   rain

## 修改人：      luoxiaowei

## 提出日期:     2024-07-06

## 原因

1. TRBW8372604房源存在一次价格错误,写的9w,后来改为了90w,当计算过卖亏之后不再计算卖亏比例导致
2. TRBW8225608房源saletp为sale,但是价格为出租价格,导致计算存在问题

## 解决办法

1. 取消计算过卖亏之后不再计算的逻辑,防止价格更改不更新问题
2. 除lsp以外再记录lspId(上次卖出房源ID),不考虑历史数据修复问题
3. 成交价格添加过滤,当价格在2w以内时认为是出租,不计算卖亏
4. 针对以上修改，添加unit test cases, propZip判断

## 确认日期:     2024-07-25

## online-step

1. 重启batch->src/batch/prop/watchPropAndCompareSoldPrice.coffee,使用init force命令,会修复历史计算错误数据,但不会添加lspId
