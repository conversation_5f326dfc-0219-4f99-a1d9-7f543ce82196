## 反馈
1. 整合 Pyfpimg流程，监控MongoDB properties表， 如果图片发生改变，生成Kafka请求,并处理Kafka响应写入MongoDB

## 需求提出人:   Fred
## 修改人:      Maggie

## 提出日期
1. 2024-06-19提出反馈

## 原因
1. 以前未提供

## 解决办法
处理流程图：Dev_floorplans/addPyfpimg.drawio
1. WatchPropAndSendFloorPlanClassification.coffee init 模式[运行watch + init]：
  处理MongoDB vow.propeties数据，同时包含phoMt时间，生成Kafka请求。
  记录reqNewestTs，reqOldestTs，reqCount，respNewestTs，respOldestTs，respCount到imgClassification collection中。
  init 部分检查当reqCount-respCount小于RUN_INIT_UNPROCESSED_THRESHOLD继续发送，否则等待
  已经发送给Kafka的properties数据会加入clsPhoSendMt字段（当前时间）。[FPC处理好的propeties数据，会加入phoClsMt字段，设置为处理时候的phoMt时间]
  如果FPL发生中途中断，需要再次运行init等情况，只处理clsPhoSendMt为空的propeties。
  如果FPL运行到某个propeties，发生错误，会把错误信息返回，写入phoClsErr: "error reason"。
  提供 -d [days] 选项用来测试，实际生产环境下全部运行，并按照从新的到旧的处理顺序。
  提供force mode，支持模型修改等情况下全部重新运行。

2. WatchPropAndSendFloorPlanClassification.coffee routine 模式[运行watch]：
  监控MongoDB vow.properties 数据, 
  如果用户新加入了图片（新加入了pho和phoMt）或者修改了图片（phoMt值发生变化），生成Kafka请求，修改clsPhoSendMt为当前时间。


  Kafka请求结构：
  ```
  {
    _id:'TRBW118118',
    uaddr:'CA:ON:MARKHAM:1 GRANDVIEW AVE',
    unt:'2',
    pics:[
        'https://cdnparap130.paragonrels.com/ParagonImages/262913989-012A77B8-BA03-41CE-A1D2-6A170394441E.JPG',
        'P/C/BOLT.png',
        'https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=1&index=1'
        ],
    phoMt: ISODate("2023-09-22T21:42:17.000+0000")},
  }
  ```
3. floorPlanClassification.coffee 处理Kafka响应消息，并同时获得FPC结果的kafka _id和phoMt，
    比较kafka phoMt和propeties phoMt：
    （1）如果一致，说明是最新数据的处理结果， 写入MongoDB vow.floorplans表，根据_id获取需要的字段内容。同时修改imgClassification collection中respNewestTs，respOldestTs，respCount。 
    （2）如果不一致，丢掉这次Kafka结果，等待最新的结果。

处理Kafka响应消息格式：
```
  {
    _id:'TRBW118188',
    uaddr:'',
    unt:'',
    phoMt: ISODate("2024-06-25T20:37:12.264+0000")，
    result:[
      {
        input:'<original-Filename-only>', 
        class:'floorplan',
        output:'<path/uuid-filename>'，
        imgHash:"823e13da5390aefbea7b8a5de965ac0dbdc759907e36c2f8b9e673cbd039f28c",
        w：20,
        h：20,  
      }  
      {
        input:'<original-Filename-only>', 
        class:'survey', 
        output:'<path/uuid-filename>',
        imgHash:"bfb",
        w：20,
        h：20  
      } 
      <!-- 返回error的图片 -->
      {
        input:'A', 
        class:'', 
        error:'URL invalid', 
      } 
      {
        input:'B', 
        class:'',
        error:'URL invalid',  
      } 
    ],
    errors:[       
      {
        id:A,
        errorMessage:'URL invalid',
      }
      {
        id:B,
        errorMessage:'URL invalid',
      }
    ]
  }
```

* 有些旧的数据photonumbers不为空，但图片url（如"https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=#{prop.sid}&index=#{prop.photonumbers}"）
已经失效，FPC会返回Error在kafka.response中，如果是五年以内的图片url失效，写入log。
errors保存到properties表phoClsErr

## 数据库表结构
存入数据库
```
{
    "_id" : ObjectId("667b24d05f0aa762f0f5c4fc"),
    "sid" : "N5310001",  //properties._id
    "tp" : "floorplan",  // get from FPC result class: floorplan/survey
    "uaddr" : "CA:ON:MARKHAM:1 GRANDVIEW AVE", // properties.uaddr
    "src" : "mls",
    "nm" : "",            //get from FPC in future
    "prov" : "On",       //properties.prov
    "city" : "Markham",  //properties.city
    "bdrms" : "2.5",     //properties.bdrms
    "bthrms" : "2",      //properties.bthrms
    "sqft" : "1870",     //properties.sqft,  get from FPC in future
    "price" : "2034990", //properties.lp or lpr 
    "imgs" : [
        {
            "img" : "n/s4UPsFJcU.png",   //get from FPC result.output(using nanoid)
            "origImg" : [                //get from FPC result.input
                "P/C/BOLS.png"
            ],
            "imgHash" : "bfbfdfdb6060000080803f3f9b9be0e1ffffffffffffffffffffffffffffffff", //get from FPC result.imgHash
            "w" : NumberInt(647),       //sharp(imgBuffer).metadata()  get from FPC
            "h" : NumberInt(1000)       //sharp(imgBuffer).metadata()  get from FPC
        }
    ],
    "unts" : [        // addtoset  helper function:isEqualUnt    //get from FPC in future
        {
            "unt" : "2"                     //properties.unt, 
            "lvl" : null,                   //properties.lvl,
            "fce" : "N"                     //properties.fce,
            "origImg" : "P/C/BOLS.png"      //get from FPC result.input
        }
        {
            "unt" : "3"
            "lvl" : null,
            "fce" : "N"
            "origImg" : "P/C/BOLS.png"
        }
    ],
    "verified" : {                         // 默认户型图not verified
        "status" : NumberInt(0)
    },
    "touchedMt" : ISODate("2024-06-25T20:13:04.201+0000"),    //batch修改记录日期
    "_mt" : ISODate("2024-06-25T20:13:04.721+0000")           //修改时间
}
```

## 上线步骤
1. 确定机器已经启动kafka。
2. 在新config中，对照[kafka.main] 和[floorplanClassification]的配置，如果有不一致的，写在.local.ini中。其他配置同appweb
3. 在rmconfig 文件夹下，运行watchPropAndSendFloorPlanClassification, 如果需要自启动，添加参数`-w 'multi-user.target'`,并按照提示操作
    ```
      ./start.sh -t batch -n watchPropAndSendFloorPlanClassification_init lib/batchBase.coffee batch/prop/watchPropAndSendFloorPlanClassification.coffee init forceInit preload-model -w 'multi-user.target' ,then do according to the log
      ./start.sh -t batch -n watchPropAndSendFloorPlanClassification_routine lib/batchBase.coffee batch/prop/watchPropAndSendFloorPlanClassification.coffee routine preload-model -w 'multi-user.target' ,then do according to the log
    ```
4. 在rmconfig 文件夹下，运行pyfpimg。 如果需要test，添加参数`--test`。 如果需要自启动，添加参数`-w 'multi-user.target'`,并按照提示操作
   ```
   ./start.sh -t pyfpimg [--test] [-w 'multi-user.target]'
   ```
5.  在rmconfig 文件夹下，运行floorPlanClassification,如果需要test，添加参数`--test`。 如果需要自启动，添加参数`-w 'multi-user.target'`,并按照提示操作
   ```
   ./start.sh -t batch -n floorPlanClassification lib/batchBase.coffee batch/prop/floorPlanClassification.coffee  init  preload-model [test] [-w 'multi-user.target]
   ```

<!-- 2. 在cfglocal的tpls/config.coffee中添加kafka 4个topic信息：   propertiesToFloorPlanClassificationInit，propertiesToFloorPlanClassificationRoutine，FloorplanClassificationResult,FloorplanClassificationResultTest。
   同时添加floorplan.imageFloorPlansOutput 和 floorplan.imageSurveysOutput
   然后运行`coffee setup.coffee` 重新生产built文件。
   ```
    kafka:
    clientId: 'batch-server'
    ignoreThisServer: false
    brokers: ['127.0.0.1:9092']
    verbose: 3
    consumerGroupId: 'batch-server'
    topic:
      condosToGetSqft: 'req.sqft.condos'
      sqftFromCondosCa: 'res.sqft.condosCa'
      propertiesToFloorPlanClassificationInit: 'fpimg-request-init'
      propertiesToFloorPlanClassificationRoutine: 'fpimg-request-routine'
      FloorplanClassificationResult: 'fpimg-response'
      FloorplanClassificationResultTest: 'test-fpimg-response'

    floorplan:
      imageFloorPlansOutput:"/tmp/floorplans"
      imageSurveysOutput:"/tmp/surveys"
    ```

1. 执行batch: src/batch/prop/watchPropAndSendFloorPlanClassification.coffee 将旧的和新修改的properties img发送到kafka。
2. 执行pyfpimg: 
   1. 修改配置文件config/config.ini，如图片是在本地，写入本地路径 [imageSource]local_path
   2. 到对应路径，运行脚本：
   ```
   cd pyfpimg
   poetry shell
   sh run.sh
   ```
3. 执行batch：src/batch/prop/floorPlanClassification.coffee 读取pyfpimg结果，写入floorplan表。
   `nohup ./start.sh lib/batchBase.coffee batch/prop/floorPlanClassification.coffee [offset or init] [test] preload-model >> ./logs/floorPlanClassification.log 2>&1 &` -->