# 需求 [fix_trebDownload]

## 反馈

1. rain反馈reImportProp 发现很多prop & rni 不同步的数据，@罗晓伟 看下是哪的问题，可能需要写个batch先同步状态
2. rain反馈trebDownloadV2#757 请求的id如果全没找到会返回错误，No Records Found，部分找到会往下执行saveRecordsBatch,需要将从evow中删除的房源从mls_treb_manual_ids中删除
3. b/src/batch/prop/fixAndReImportProps.coffee manual_records 和 master都要查，如果都找到要对比mt，因为有的record从evow撤出后是手动进来的(N8126342)
4. mls_treb_manual_ids 和 /src/mlsImport/trebDownloadV2.coffee(getAvailables) 需要区分CondoProperty/ResidentialProperty
5. 手动import的前端需要添加CondoProperty/ResidentialProperty类型筛选(/1.5/import/manual)

## 需求提出人:   rain

## 修改人：      luoxiaowei

## 提出日期:     2024-05-09

## 原因

1. 反馈1因为status===A的新房源import对lst进行了重新赋值为New,导致batch判断lst不一致

## 解决办法

1. 针对反馈1
   1. 需要修改saveToMaster对status===A的新房源不进行lst重新赋值,使用rni的值
   2. 修改batch->reImport对status===A房源的lst判断(已经和fred确认)
   3. 添加batch对status不一致的房源重新导入。(fixPropStatus2 仅更新了status,不会更新price,lst等信息 不能复用)
2. 针对反馈2 需要在判断No Records Found后,执行trebManualCheck.checkMissingProps 函数,将evow中删除的房源从mls_treb_manual_ids中删除
<!-- TODO:对于rni与vow房源状态不一致的情况,先调查看能否确认原因,若不能确认需要在状态更新时添加log记录 -->

## 确认日期:     2024-05-09
