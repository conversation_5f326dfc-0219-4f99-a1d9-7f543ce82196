# 需求 [add_equifax]

## 反馈

1. 添加EquiFax

## 需求提出人:   fred

## 修改人：      Maggie

## 提出日期:
1. 2024-07-26

## 解决办法

1. 添加lib/equifax.coffee, 调用equifax接口，获取token，consumer report xml， 并[将xml转js object](https://www.npmjs.com/package/xml2js)。
2. 在输入中添加unit，如果有unit，合并到address的street name中。
3. 添加model/equifax.coffee，保存输入，输出xml，输出object到数据库。
4. 添加接口文件80-sites下
5. 补充接口文档及流程图 docs/Dev_equifax

## 确认日期:    2024-07-18

## online step[new config]
1. 在rmconfig Add_equifax branch 合并后，在local.ini 中添加对应的equifax信息
   ```
  [equifax]
  clientId='O5OqoO8s8RfyjKyyGF1wdVVWrtPsTJXu'
  clientSecret='4v8UtPc5Cj88yZjf'
  db='vow'
  col='equifax'

  [equifax.api]
  token = 'https://api.uat.equifax.ca/v2/oauth/token'
  report =  'https://api.uat.equifax.ca/inquiry/1.0/sts'
  scope = 'https://api.equifax.ca/inquiry/1.0/sts'

  [equifax.customer]
  customerCode = 'R346'
  customerNumber = '999RE00254'
  securityCode = '99'
  customerId = 'for billing get from Clients'
  customerReferenceNumber = '2495'
   ```
   