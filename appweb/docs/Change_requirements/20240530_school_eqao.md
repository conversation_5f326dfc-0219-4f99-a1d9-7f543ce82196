# 需求 [school_eqao]

## 反馈

1. rain反馈我们eqao的高中有数据的只有281个，理论上有至少780个，我刚才查了一个board有80个，这个可能要重跑下src/batch/puppeteer/src/school_eqao/school_eqao.js

## 需求提出人:   rain

## 修改人：      liurui

## 提出日期:     2024-05-30

## 原因
之前在eqao网站获取的学校数据不全

## 解决办法
1. 更新board数据
   1. batch添加Board获取回来的数据为data
      1. 表里存在对应board增加eqao字段，保存数据
        .eqao:data
      2. 表里不存在对应board
        data._id: eqaoname全大写，去除符号，更换法语字母
        data.eqao:data
        data.src:'eqao'

2. 获取新board中学校的数据
3. 根据之前的匹配规则，更新eqao数据至school


## 确认日期:     2024-06-05

