# 需求 [fix_treb]

## 反馈

sales群里反馈TRBC12191180已经sold，但状态没更新

## 需求提出人:    Amy Ni
## 修改人：       Maggie

## 提出日期:      2025-06-23

## 原因
查找C12191180 接口返回已经sold["ModificationTimestamp": "2025-06-12T14:28:42Z",]，但是在logs中没有sold的记录，查询event_log,_id: 'routine-mainLoop-evow-2025-06-12T14:30:51.762Z'记录中正常，没有error。



## 解决办法
1. 修改dup，duplog全部保存
2. query 时间 往前1min
3. 添加batch reDownloadRni.coffee，每5分钟查询 _mt 字段在3天或7天前对应时间窗口（5分钟时间段）内的房源数据，重新加入下载队列，一直运行。


## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-23

## online-step

1. 重启trebResoDownload
2. 重启carDownload
3. 重启raeDownload
4. dryrun reDownloadRni.coffee
```
./start.sh -t batch -n reDownloadRni -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a [days]"
```
5. cronjob reDownloadRni.coffee
```
# days 3
./start.sh -t batch -n reDownloadRni_day3 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 3" -cron '*:0/5'

# days 7
./start.sh -t batch -n reDownloadRni_day7 -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a 7" -cron '*:0/5'
```
