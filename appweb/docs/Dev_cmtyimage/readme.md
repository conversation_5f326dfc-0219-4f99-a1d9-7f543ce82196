## db setup
download and dump collection cmty_image to local db

## cmty image setup
clone rmlfs repo on same level of realmaster-appweb
go src
./bin/imgstocks.sh

## confirm on prop detail page
go to property page, check location cmty tab

## update cmty image or bbox
login to realmaster website as dev/admin
go to vip console
go to generateCmty Image
url query ?id=${uaddr}
can update one or update all

## update new cmty image
if there is new images, after generate the new images,
./bin/imgstocks.sh compress
to get new compress file on rmlfs/imgstocks/cmtys.tar.gz
push to server and uncompress to test

## files
front end component: src/coffee4client/components/frac/PropDetail.vue
admin: src/themes/website/admin/cmtyStaticMap.html
helper lib: src/libapp/boundaryHelper.coffee
controller: src/apps/80_sites/WebRM/census/admin.coffee