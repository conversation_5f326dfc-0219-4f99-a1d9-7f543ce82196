<mxfile host="65bd71144e">
    <diagram id="Yx2KndvVmbI9TZiKlKFj" name="第 1 页">
        <mxGraphModel dx="1917" dy="394" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="184" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=457;entryDy=38;entryPerimeter=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;startArrow=none;dashed=1;dashPattern=12 12;" parent="1" source="185" target="275" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="934.5" y="44.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="194" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;" parent="1" source="2" target="193" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="geo_cache按id统计房源数量" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="20" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="180" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;entryX=0;entryY=0;entryDx=717;entryDy=53;entryPerimeter=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;startArrow=none;dashed=1;dashPattern=12 12;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="1" source="176" target="277" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="30" y="150" as="sourcePoint"/>
                        <mxPoint x="-55.5" y="125.5" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="30" y="143"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="197" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;" parent="1" source="3" target="196" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="&lt;div style=&quot;&quot;&gt;&lt;span style=&quot;background-color: initial;&quot;&gt;遍历geocache_propCnt，按lat-lng，将距离相差20m以内geo_cache分组&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
                    <mxGeometry x="230" y="204" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="65" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="4" target="48" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="查询nearby_uaddr，得到cursor" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="390" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="66" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="48" target="50" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="处理cursor.uaddr,得到&lt;br&gt;st(main words),st_num,unit,dir,st_sfx&lt;br&gt;(prorAddress.&lt;span style=&quot;background-color: rgb(30, 30, 30);&quot;&gt;formatProvAndCity)&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="230" y="480" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="67" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="50" target="51" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="对cursor.uaddr按st_num分组,得到st_num_obj" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="570" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="68" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="1" source="51" target="52" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="对st_num_obj的uaddr遍历计算差异度，按照差异度(升序)与propCnt(倒序)排序" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="670" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="69" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="52" target="53" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="52" value="取第一位作为归一后的uaddr，将其余uaddr(q&amp;gt;=100)在geo_cache中删除，并添加到aUaddr中" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="770" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="205" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=default;fontColor=#FF0000;endSize=6;targetPerimeterSpacing=6;" parent="1" source="53" target="204" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="53" value="记录进行归一处理的uaddr信息" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="870" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="74" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="1" source="75" target="72" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="84" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;startArrow=none;" parent="1" source="81" target="78" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="70" value="cursor.hasNext()" style="rhombus;whiteSpace=wrap;html=1;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="230" y="1080" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="72" value="end" style="whiteSpace=wrap;html=1;rounded=1;" parent="1" vertex="1">
                    <mxGeometry x="230" y="1180" width="250" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="77" value="" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="1" source="70" target="75" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="355" y="1130" as="sourcePoint"/>
                        <mxPoint x="355" y="1180" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="75" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="335" y="1140" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="86" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="78" target="48" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="95" y="560"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="78" value="cursor = cursor.next()" style="whiteSpace=wrap;html=1;" parent="1" vertex="1">
                    <mxGeometry x="30" y="690" width="130" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="85" value="" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=none;" parent="1" source="70" target="81" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="230" y="915" as="sourcePoint"/>
                        <mxPoint x="95" y="640" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="95" y="910"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="81" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="75" y="861" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="181" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeColor=default;endSize=6;endArrow=none;dashed=1;dashPattern=8 8;" parent="1" source="3" target="176" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="230" y="195" as="sourcePoint"/>
                        <mxPoint x="-55.5" y="125.5" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="160" y="229"/>
                            <mxPoint x="160" y="144"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="176" value="处理逻辑" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="31" y="129" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="186" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=default;endSize=6;endArrow=none;dashed=1;dashPattern=12 12;" parent="1" source="2" target="185" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="480" y="95" as="sourcePoint"/>
                        <mxPoint x="934.5" y="44.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="185" value="处理逻辑" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
                    <mxGeometry x="720" y="30" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="195" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;" parent="1" source="193" target="3" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="193" value="tmp:geocache_propCnt" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#1A1A1A;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="247" y="111" width="216" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="198" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=default;endSize=6;targetPerimeterSpacing=6;" parent="1" source="196" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="196" value="tmp:&lt;span style=&quot;text-align: left;&quot;&gt;&amp;nbsp;nearby_uaddr&lt;/span&gt;" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#1A1A1A;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="247" y="290" width="216" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="206" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeColor=default;fontColor=#FF0000;endSize=6;targetPerimeterSpacing=6;" parent="1" source="204" target="70" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="204" value="tmp:&lt;span style=&quot;text-align: left;&quot;&gt;&amp;nbsp;&lt;/span&gt;normalize_uaddr_log" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#1A1A1A;gradientColor=none;" parent="1" vertex="1">
                    <mxGeometry x="247" y="970" width="216" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="276" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="930" y="5" width="470" height="495" as="geometry"/>
                </mxCell>
                <mxCell id="275" value="" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillStyle=auto;fontColor=#FF0000;fillColor=#18141D;gradientColor=none;direction=north;size=76;" parent="276" vertex="1">
                    <mxGeometry width="470" height="495" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="查询geo_cache，得到cursor" style="whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="120" y="15" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="存在uaddr===cursor._id.&lt;br&gt;ptype2 in [Apartment,Condo]&lt;br&gt;的房源" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="60" y="95" width="240" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="25" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="276" source="5" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="统计uaddr===cursor._id&lt;br&gt;的房源数量" style="whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="120" y="203" width="120" height="45" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="保存记录到geocache_propCnt" style="whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="120" y="279" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="19" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="276" source="9" target="10" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="end" style="whiteSpace=wrap;html=1;rounded=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="120" y="445" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="24" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="276" source="37" target="15" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="cursor.hasNext()" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="120" y="355" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="23" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="276" source="10" target="22" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="30" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;" parent="276" source="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="180" y="75" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="cursor=cursor.next()" style="whiteSpace=wrap;html=1;container=0;" parent="276" vertex="1">
                    <mxGeometry x="340" y="55" width="120" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="276" source="8" target="33" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="180" y="155" as="sourcePoint"/>
                        <mxPoint x="180" y="203" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="33" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="276" vertex="1">
                    <mxGeometry x="160" y="165" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="276" source="33" target="9" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;" parent="276" source="22" target="35" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="240" y="380" as="sourcePoint"/>
                        <mxPoint x="400" y="95" as="targetPoint"/>
                        <Array as="points"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="35" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="276" vertex="1">
                    <mxGeometry x="300" y="370" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="28" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;startArrow=none;" parent="276" source="35" target="27" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="400" y="305"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="41" value="" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="276" source="22" target="37" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="180" y="405" as="sourcePoint"/>
                        <mxPoint x="180" y="445" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="37" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="276" vertex="1">
                    <mxGeometry x="160" y="415" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="276" source="8" target="44" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="60" y="125" as="sourcePoint"/>
                        <mxPoint x="120" y="380" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="40" y="145"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="44" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="276" vertex="1">
                    <mxGeometry x="20" y="248" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="43" style="edgeStyle=elbowEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;startArrow=none;" parent="276" source="44" target="22" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <Array as="points">
                            <mxPoint x="40" y="255"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="279" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="-770" y="90" width="770" height="590" as="geometry"/>
                </mxCell>
                <mxCell id="277" value="" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillStyle=auto;fontColor=#FF0000;fillColor=#18141D;gradientColor=none;size=106;" parent="279" vertex="1">
                    <mxGeometry width="770" height="590" as="geometry"/>
                </mxCell>
                <mxCell id="126" value="查询&lt;span style=&quot;text-align: left;&quot;&gt;geocache_propCnt得到cursor&lt;/span&gt;" style="whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="298" y="20" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="127" value="对cursor.lat,cursor.lng保留四位小数，得到editLat,editLng" style="whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="298" y="110" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="137" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="279" source="126" target="127" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="128" value="存在&quot;#{editLat},#{editLng}&quot;记录" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="293" y="200" width="210" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="138" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="279" source="127" target="128" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="129" value="添加记录到&lt;span style=&quot;text-align: left;&quot;&gt;nearby_uaddr&lt;/span&gt;" style="whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="298" y="326" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="155" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="279" source="130" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="398" y="390" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="658" y="390"/>
                            <mxPoint x="398" y="390"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="130" value="更新&lt;span style=&quot;text-align: left;&quot;&gt;nearby_uaddr记录&lt;/span&gt;" style="whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="558" y="326" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="132" value="cursot.hasNext()" style="rhombus;whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="293" y="417" width="210" height="56" as="geometry"/>
                </mxCell>
                <mxCell id="140" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="279" source="129" target="132" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="134" value="end" style="whiteSpace=wrap;html=1;rounded=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="298" y="543" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="144" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="279" source="135" target="127" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="135" value="cursor=cursor.next()" style="whiteSpace=wrap;html=1;container=0;" parent="279" vertex="1">
                    <mxGeometry x="18" y="208" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="147" value="" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="279" source="132" target="146" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="398" y="473" as="sourcePoint"/>
                        <mxPoint x="398" y="543" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="146" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="279" vertex="1">
                    <mxGeometry x="378" y="500" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="141" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="279" source="146" target="134" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="149" value="" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;endArrow=none;" parent="279" source="128" target="148" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="398" y="256" as="sourcePoint"/>
                        <mxPoint x="398" y="326" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="148" value="no" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="279" vertex="1">
                    <mxGeometry x="378" y="284" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="139" style="edgeStyle=none;rounded=0;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="279" source="148" target="129" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="151" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;" parent="279" source="128" target="150" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="503" y="228" as="sourcePoint"/>
                        <mxPoint x="658" y="326" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="150" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="279" vertex="1">
                    <mxGeometry x="588" y="218" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="142" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;startArrow=none;" parent="279" source="150" target="130" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="153" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=none;" parent="279" source="132" target="152" edge="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="293" y="445" as="sourcePoint"/>
                        <mxPoint x="118" y="248" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="152" value="yes" style="text;strokeColor=none;align=center;fillColor=none;html=1;verticalAlign=middle;whiteSpace=wrap;rounded=0;container=0;" parent="279" vertex="1">
                    <mxGeometry x="98" y="380" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="143" style="edgeStyle=orthogonalEdgeStyle;rounded=0;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;startArrow=none;" parent="279" source="152" target="135" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="159" value="保存addr,city,cnty,prov&lt;br&gt;cmty,lat,lng,lat,lng,&lt;br&gt;st,st_num,st_sfx,propCnt" style="shape=note;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=west;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=0;pointerEvents=1;rounded=0;align=center;whiteSpace=wrap;container=0;" parent="279" vertex="1">
                    <mxGeometry x="530" y="400" width="200" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="282" value="_id: 归一后的uaddr&lt;br&gt;replaceUaddr:需要删除的uaddr&lt;br&gt;[{uaddr,lat,lng,q,st_sfx}]" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=south;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=20;pointerEvents=1;align=left;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="100" y="950" width="175" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="299" value="&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;unit与st_num判断逻辑&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp; &amp;nbsp; unit:&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;/span&gt;- 纯数字,eg:123&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- 数字组合,eg: 1&amp;amp;1,1/1,1\1,1+1,#1,PH1,1A&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- 单字符,eg:A,B&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp; &amp;nbsp; st_num:&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- 纯数字,eg:123&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- 数字-数字,eg:1-2&lt;/span&gt;&lt;br style=&quot;&quot;&gt;&lt;span style=&quot;&quot;&gt;&amp;nbsp; &amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;- 数字加单字符,eg:1A&lt;br&gt;&lt;br&gt;&lt;/span&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;&lt;font color=&quot;#ff0000&quot;&gt;cases:&lt;/font&gt;&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;301 2842-2856 Gottingen St&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;/span&gt;unit=301,st_num='2842-2856'&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;20282 72b Ave&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=20282&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;412 4 Ave&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=412&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;138 Downes St&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=138&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;210 G4 4653 Blackcomb Way&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;unit=210 G4,st_num=4653&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;#lph4803 -50 Charles St E&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;unit=lph4803,st_num=50&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;2908 Hwy 7&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=2908&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;2908 Hwy 7 Rd&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=2908&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;201 10th St&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=210&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;Ph817 Spadina Ave&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;error&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;San Romanoway&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;error&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;404&amp;amp;410 Dundas St S&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;error&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; font-size: 13px; line-height: normal; font-family: &amp;quot;Helvetica Neue&amp;quot;; margin: 0px;&quot; class=&quot;p1&quot;&gt;Key Cove 1 Ave&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;error&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; margin: 0px;&quot; class=&quot;p1&quot;&gt;&lt;font face=&quot;Helvetica Neue&quot;&gt;&lt;span style=&quot;font-size: 13px;&quot;&gt;Lot 32 823 Beacon Crt&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;st_num=Lot 32&lt;/span&gt;&lt;/font&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; margin: 0px;&quot; class=&quot;p1&quot;&gt;&lt;font face=&quot;Helvetica Neue&quot;&gt;&lt;span style=&quot;font-size: 13px;&quot;&gt;A301[aug]-810 Humboldt St&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;unit=A301 aug,st_num=810&lt;/span&gt;&lt;br&gt;&lt;/font&gt;&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; margin: 0px;&quot; class=&quot;p1&quot;&gt;&lt;font face=&quot;Helvetica Neue&quot;&gt;&lt;span style=&quot;font-size: 13px;&quot;&gt;(abcd) 1351 Gerry Sorensen Way&amp;nbsp; &amp;nbsp;&amp;nbsp;st_num=1351&lt;/span&gt;&lt;br&gt;&lt;/font&gt;&lt;/p&gt;&lt;p style=&quot;font-variant-numeric: normal; font-variant-east-asian: normal; font-stretch: normal; line-height: normal; margin: 0px;&quot; class=&quot;p1&quot;&gt;&lt;font face=&quot;Helvetica Neue&quot;&gt;&lt;span style=&quot;font-size: 13px;&quot;&gt;204 (a&amp;amp;b) - 1549 Kicking Horse&amp;nbsp;&lt;span style=&quot;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;&lt;span style=&quot;white-space: pre;&quot;&gt;&#9;&lt;/span&gt;&amp;nbsp;unit=204 (a&amp;amp;b),st_num=1549&lt;/span&gt;&lt;/font&gt;&lt;/p&gt;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;fontColor=#000000;darkOpacity=0.05;fillColor=#FFF9B2;strokeColor=none;fillStyle=solid;direction=north;gradientDirection=north;gradientColor=#FFF2A1;shadow=1;size=20;pointerEvents=1;align=left;verticalAlign=top;" parent="1" vertex="1">
                    <mxGeometry x="495" y="500" width="510" height="430" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>