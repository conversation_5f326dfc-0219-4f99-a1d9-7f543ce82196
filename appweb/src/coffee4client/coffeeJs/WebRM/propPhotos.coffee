# 使用命名空间避免全局变量污染
PhotoManager = 
  photosGallery: null
  swiper: null
  loadingInProgress: false
  timeoutId: null

# 配置常量
CONFIG = 
  PRELOAD_TIMEOUT: 5000
  SWIPER_DETAIL_OPTIONS:
    slidesPerView: 'auto'
    spaceBetween: 10
    freeMode: true
    grabCursor: true
    navigation:
      nextEl: '.swiper-button-next'
      prevEl: '.swiper-button-prev'
    observer: true
    observeParents: true
  SWIPER_GALLERY_OPTIONS:
    preloadImages: false #Disable preloading of all images
    lazy: true #Enable lazy loading
    slidesPerView: 1
    centeredSlides: true
    freeMode: true
    grabCursor: true
    observer: true
    observeParents: true
    navigation:
      nextEl: '#photo-gallery-next'
      prevEl: '#photo-gallery-prev'
    keyboard:
      enabled: true
      onlyInViewport: false

$(document).ready ->
  
  # 清理图片事件监听器
  cleanupImageListeners = (img) ->
    $(img).off('load error')
  
  # 监听前3张图片的加载完成
  waitForPreloadImages = ->
    preloadImages = $('.preload-image')
    loadedCount = 0
    totalPreloadImages = preloadImages.length
    if totalPreloadImages is 0
      # 如果没有预加载图片，直接加载其余图片
      loadRemainingImages()
      return
    
    # 完成加载的回调函数
    onLoadComplete = ->
      if loadedCount >= totalPreloadImages
        # 清理所有事件监听器
        preloadImages.each (index) ->
          cleanupImageListeners(this)
        
        # 清理超时定时器
        if PhotoManager.timeoutId
          clearTimeout(PhotoManager.timeoutId)
          PhotoManager.timeoutId = null
        
        loadRemainingImages()

    preloadImages.each (index) ->
      img = this
      
      if img.complete and img.naturalWidth > 0
        # 图片已经加载完成
        loadedCount++
        onLoadComplete()
      else
        # 监听图片加载完成事件
        $(img).on 'load', ->
          loadedCount++
          onLoadComplete()
        
        # 监听图片加载错误事件
        $(img).on 'error', ->
          loadedCount++
          onLoadComplete()
    
    # 添加一个超时机制，防止永远等待
    PhotoManager.timeoutId = setTimeout ->
      if loadedCount < totalPreloadImages
        # 清理所有事件监听器
        preloadImages.each (index) ->
          cleanupImageListeners(this)
        loadRemainingImages()
    , CONFIG.PRELOAD_TIMEOUT
  
  # 加载其余图片
  loadRemainingImages = ->
    # 防止竞态条件
    return if PhotoManager.loadingInProgress
    PhotoManager.loadingInProgress = true
    pendingSlides = $('.pending-load')
    
    pendingSlides.each (index) ->
      slide = $(this)
      imgSrc = slide.data('src')
      imgIndex = slide.data('index')
      
      if imgSrc and imgIndex isnt undefined
        # 替换占位符为真实图片
        slide.html('<img class="detail-photo" data-index="' + imgIndex + '" src="' + imgSrc + '">')
        slide.removeClass('pending-load')
    # 更新Swiper以识别新添加的slides
    if PhotoManager.swiper
      PhotoManager.swiper.update()
    PhotoManager.loadingInProgress = false
    
  
  # 使用事件委托来处理动态添加的图片点击事件
  $(document).on 'click', '.detail-photo', ->
    if not PhotoManager.photosGallery
      PhotoManager.photosGallery = new Swiper('#photos-gallery', CONFIG.SWIPER_GALLERY_OPTIONS)
    index = $(this).data('index')
    PhotoManager.photosGallery.slideTo(index, false, false)
    $('#photos-gallery').show()
  # 初始化主要的详情图片Swiper
  PhotoManager.swiper = new Swiper('#detail-photos', CONFIG.SWIPER_DETAIL_OPTIONS)
  
  # 开始监听预加载图片
  waitForPreloadImages()
