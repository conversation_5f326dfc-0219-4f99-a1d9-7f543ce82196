<!-- Generated by SpecStory -->

# 细化修改内容和代码位置建议 (2025-06-16 09:19:13)

_**User**_

参考 @20250613_deal_claim.md 文件的 解决办法 部分，找出对应的修改位置，细化修改内容

---

_**Assistant**_

根据你提供的 `20250613_deal_claim.md` 文件的“解决办法”部分，下面是每一条修改建议的细化说明和对应的代码位置建议：

---

### 1. claim.coffee 修改 gClaimRange 限制为 30 天

**细化内容：**
- 找到 `claim.coffee` 文件中定义 gClaimRange 的地方（通常是一个常量或配置项）。
- 将其值设置为 30（天）。
- 检查所有用到 gClaimRange 的逻辑，确保限制生效。

**代码位置建议：**
- 可能在 `appweb/batch/claim.coffee` 或 `src/batch/claim.coffee`，具体路径需确认。
- 搜索 `gClaimRange` 关键字定位。

---

### 2. claim.coffee checkIfCanClaim 增加判断 saleTypeTag_en == 'Sold'

**细化内容：**
- 找到 `claim.coffee` 文件中的 `checkIfCanClaim` 方法或函数。
- 在判断逻辑中增加一条：只有当 `saleTypeTag_en` 字段为 `'Sold'` 时，才允许认领。
- 如果是 BC 地区，可能是 `Closed`，需根据地区适配。

**代码位置建议：**
- 依然在 `claim.coffee` 文件。
- 搜索 `checkIfCanClaim` 关键字定位。

---

### 3. /1.5/mapSearch?appMapMode=fav&mode=map 支持 appMapMode = claim 参数

**细化内容：**
- 找到 `/1.5/mapSearch` 接口的实现文件（可能在 `appweb/src/appDev3/mapSearch.js` 或类似文件）。
- 在参数解析处，支持 `appMapMode=claim`。
- 当为 `claim` 时，调用 `post '/propClaim/list'` 接口，获取已认领房源，并在地图上高亮显示。
- 需要前端和后端配合，前端负责传参和展示，后端负责数据返回。

**代码位置建议：**
- 后端接口实现文件 `/1.5/mapSearch`。
- 前端地图页面相关文件，可能在 `appweb/src/appDev3/saves/properties.js`、`appweb/src/appDev3/saves/deals.js`。

---

### 4. get '/1.5/saves/deals' 右上角可以点击 MAP 跳转到 mapsearch 页面

**细化内容：**
- 在 `/1.5/saves/deals` 页面右上角增加一个 MAP 按钮。
- 点击后跳转到 `/1.5/mapSearch?appMapMode=claim` 页面。
- 参考 `appweb/src/appDev3/saves/properties.js` 的实现，修改 `appweb/src/appDev3/saves/deals.js`。

**代码位置建议：**
- 前端页面 `appweb/src/appDev3/saves/deals.js`。
- 参考 `appweb/src/appDev3/saves/properties.js` 的 MAP 跳转逻辑。

---

### 5. 当 appMapMode = claim，通过 post '/propClaim/list' 获取已经 claim 的房源显示在地图上

**细化内容：**
- 在地图页面逻辑中，判断 `appMapMode=claim` 时，调用 `post '/propClaim/list'`。
- 获取已认领房源数据后，在地图上进行特殊标记或高亮显示。

**代码位置建议：**
- 地图页面的前端逻辑文件（如 `appweb/src/appDev3/mapSearch.js` 或相关组件）。
- 后端 `/propClaim/list` 接口实现文件。

---

### 总结

#### 主要涉及文件
- `appweb/batch/claim.coffee` 或 `src/batch/claim.coffee`
- `appweb/src/appDev3/saves/deals.js`
- `appweb/src/appDev3/saves/properties.js`（参考）
- `appweb/src/appDev3/mapSearch.js`（或地图相关文件）
- `/propClaim/list` 和 `/1.5/mapSearch`、`/1.5/saves/deals` 的接口实现

#### 主要修改点
1. gClaimRange 限制为 30 天
2. checkIfCanClaim 增加 saleTypeTag_en == 'Sold' 判断
3. 地图支持 appMapMode=claim，调用 propClaim/list
4. deals 页面增加 MAP 跳转
5. 地图上高亮已认领房源

如需进一步细化到每个函数/代码块级别，请告知具体 claim.coffee、mapSearch.js 等文件的路径，我可以帮你定位和生成详细注释代码。

---

# Implement post endpoint with permission checks (2025-06-17 10:55:38)

_**User**_

参考 @propClaim.coffee 168-191行的权限判断，创建 post ‘map'， @claim.coffee 中添加function 参考 getlist 生成query条件，获取对应数据的pid字段，使用await formatProp {ids,req} 找到proplist并返回 ，返回的数据格式为 {ok:1,item:[proplist]}

---

_**Assistant**_

```diff
- 
  config = CONFIG(['serverBase','share'])
  debug = DEBUG()
```

```diff
      catch err
        debug.error err
-       return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
+       return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), category:MSG_STRINGS.DB_ERROR,resp}
      if (not req.isAllowed 'claimAdmin') and (not isRealGroup)
        debug.error {msg:"editClaim:#{MSG_STRINGS.NO_AUTHORIZED}",uid:uid,eml:user.eml}
```

```diff
      return resp.send {ok:1,list:returnList}
  
+ ###
+ # 根据参数获取claim相关房源的proplist
+ # @body: 查询参数，参考getList
+ # @return: {ok:1,item:[proplist]}
+ ###
+ POST 'map', (req, resp) ->
+   UserModel.appAuth {req, resp}, (user) ->
+     return respError {category: MSG_STRINGS.NEED_LOGIN, errorCode: 0, resp} unless user
+     uid = user._id
+     try
+       isRealGroup = await GroupModel.isInGroup {uid, groupName: ':inReal'}
+     catch err
+       debug.error err
+       return respError {clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), category: MSG_STRINGS.DB_ERROR, resp}
+     isNoteAdmin = req.isAllowed 'claimAdmin'
+     body = req.body
+     if (not isNoteAdmin) and ((not isRealGroup) or body?.agent)
+       debug.error {msg: "get Claim map:#{MSG_STRINGS.NO_AUTHORIZED}", uid, eml: user.eml}
+       return respError {clientMsg: req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
+     try
+       ids = await ClaimModel.getMapPids {uid, body, isNoteAdmin}
+       proplist = await formatProp {ids, req}
+     catch err
+       debug.error err
+       return respError {clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), sysErr: err, resp}
+     return resp.send {ok: 1, item: Object.values(proplist)}
  
  formatProp = ({ids,req})->
```

---

```diff
      ])
  
+ ###
+   # 获取claim相关的所有pid，返回pid数组
+   # @param {object} {uid,body,isNoteAdmin} - 查询参数，参考getList
+   # @return [pid]
+ ###
+   @getMapPids:({uid,body,isNoteAdmin})->
+     body.sort ?= 'tsNum'
+     query = buildSearchQuery body
+     if isNoteAdmin
+       delete query.uid
+       if body.agent
+         query.uid = new ObjectId(body.agent)
+     else
+       query.uid = new ObjectId(uid)
+     // 只查pid字段
+     docs = await ClaimCol.find(query, {projection: {pid: 1}}).toArray()
+     pids = docs.map((d) -> d.pid)
+     # 去重
+     Array.from(new Set(pids))
+ 
  ###
    # 获取用户claim百分比信息
```

---

```diff
      else
        query.uid = new ObjectId(uid)
-     // 只查pid字段
+     # 只查pid字段
      docs = await ClaimCol.find(query, {projection: {pid: 1}}).toArray()
      pids = docs.map((d) -> d.pid)
```

---

已完成以下功能开发：

1. 在 `propClaim.coffee` 中新增了 POST 'map' 接口，权限校验与 list 保持一致，调用 `ClaimModel.getMapPids` 获取 pid 列表，并用 `formatProp` 返回房源详情，格式为 `{ok:1, item:[proplist]}`。
2. 在 `claim.coffee` 中新增了静态方法 `getMapPids`，复用 getList 的 query 逻辑，仅返回所有匹配数据的唯一 pid 数组。
3. 修复了注释风格导致的 linter 错误。

如需接口注释或进一步测试/联调，请告知！

---

# Prevent doSearch call in mounted (2025-06-17 14:25:06)

_**User**_

检查文件运行逻辑，我在mounted中添加了vars.appMapMode == 'claim'，不希望之后再调用 dosearch

---

_**Assistant**_

你的需求是：在 `mounted` 钩子中，如果 `vars.appMapMode == 'claim'`，只执行 `getClaimedProps()`，**后续不要再自动调用 `doSearch`**。  
目前你的 `mounted` 逻辑如下：

- 如果 `vars.appMapMode == 'claim'`，会执行 `getClaimedProps()`。
- 但**后面还有很多代码**，比如：
  - `if (!this.scrollElement) { ... }`
  - `if (vars.saletp == 'lease') { ... }`
  - 还有一大段关于 `vars.mapmode`、`vars.city`、`filterFromUrl`、`parseSerializedFilter` 等的处理。
  - 这些代码**可能会触发 `doSearch`**，比如 `if (vars.mode == 'list')`、`if (vars.mode == 'map')` 等。

### 你的问题

**你不希望在 `vars.appMapMode == 'claim'` 时，后续再自动调用 `doSearch`。**

---

## 解决建议

你需要在 `mounted` 里**提前 return**，让后续逻辑不再执行。  
比如：

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return; // 这里提前 return，后续代码不会再执行
}
```

这样，只有 `getClaimedProps()` 会被调用，后续不会再自动触发 `doSearch`。

---

## 代码位置建议

在 `mounted()` 里，找到如下代码段：

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
}
```

**修改为：**

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return; // 防止后续再自动 doSearch
}
```

---

## 结论

只需在 `vars.appMapMode == 'claim'` 逻辑后加 `return`，即可保证 mounted 后续不会再自动调用 `doSearch`。  
如需我帮你直接修改代码，请告知！

---

_**User**_

这个时候地图加载出来了吗

---

_**Assistant**_

你的问题是：  
**在 `mounted` 里，如果 `vars.appMapMode == 'claim'`，直接 return，地图（mapbox/mapObj）会加载出来吗？**

---

### 代码分析

在 `mounted` 钩子里，地图相关的初始化逻辑主要有：

```js
if (vars.src !== 'nativeMap') {
  this.mapObj = this.getMapObj();
  if (vars.mode!=='list') {
    this.loadMapBoxScript();
    this.initGmap();
  }
  if (vars.mode == 'list') {
    setTimeout(()=>{
      self.doSearch({noClear:true});
    },100)
  }
}
```

- 这段代码是在 `mounted` 的**后半部分**。
- 如果你在 `vars.appMapMode == 'claim'` 时直接 `return`，**这段代码不会被执行**。

---

### 结论

**不会加载地图。**
- 因为你提前 return，`this.mapObj = this.getMapObj()`、`this.loadMapBoxScript()`、`this.initGmap()` 都不会执行。
- 这样页面不会初始化地图对象，也不会显示地图。

---

### 解决建议

如果你希望在 `claim` 模式下**依然加载地图**，但不自动 doSearch，可以这样调整：

1. **先初始化地图相关内容**
2. **再判断是否 claim 并 return**

例如：

```js
if (vars.src !== 'nativeMap') {
  this.mapObj = this.getMapObj();
  if (vars.mode!=='list') {
    this.loadMapBoxScript();
    this.initGmap();
  }
  if (vars.mode == 'list') {
    setTimeout(()=>{
      self.doSearch({noClear:true});
    },100)
  }
}

if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return;
}
```

这样，地图会被加载，但不会自动 doSearch。

---

#### 总结

- **提前 return 会导致地图不加载。**
- 如果你要地图加载但不 doSearch，return 要放在地图初始化之后。

如需我帮你调整代码，请告知！

---

_**User**_

调整代码

---

_**Assistant**_

<details>
            <summary>Searched codebase "vars.appMapMode == 'claim'" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L261-317 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1812-1840 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L317-362 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2009-2036 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2982-3032 |
| `appweb/src/coffee4client/components/appEvaluationPage.vue` | L123-186 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L4037-4088 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1931-1960 |
| `appweb/src/coffee4client/components/appPropDetailPage.vue` | L401-460 |
| `appweb/src/coffee4client/components/evaluate_mixins.js` | L145-159 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L2295-2366 |
| `appweb/src/coffee4client/components/directionSearch-mapbox.vue` | L165-221 |
| `appweb/src/coffee4client/components/appPropPredictionPage.vue` | L69-153 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2859-2921 |
| `appweb/src/coffee4client/components/mixin/mapBasic.js` | L42-96 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1098-1147 |
| `appweb/src/coffee4client/components/showing/showingDetail.vue` | L286-323 |
| `appweb/src/coffee4client/components/appMapMixView.vue` | L118-162 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L1358-1406 |
| `appweb/src/coffee4client/components/appPropPredictionPage.vue` | L153-222 |
| `appweb/src/coffee4client/components/directionSearch.vue` | L161-218 |
| `appweb/src/coffee4client/components/appPropDetailPage.vue` | L86-172 |
| `appweb/src/coffee4client/components/project/appProjManage.vue` | L499-579 |
| `appweb/src/coffee4client/components/webMap.vue` | L470-551 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L175-224 |

</details>

---

```diff
        this.appMapMode = vars.appMapMode;
      }
+     // 地图初始化逻辑提前
+     if (vars.src !== 'nativeMap') {
+       this.mapObj = this.getMapObj();
+       if (vars.mode!=='list') {
+         this.loadMapBoxScript();
+         this.initGmap();
+       }
+       if (vars.mode == 'list') {
+         setTimeout(()=>{
+           self.doSearch({noClear:true});
+         },100)
+       }
+     }
+     // 处理 appMapMode=claim 的情况，提前 return，防止后续 doSearch
+     if (vars.appMapMode == 'claim') {
+       this.viewMode = 'map';
+       this.getClaimedProps();
+       return;
+     }
      if (vars.grp != null) {
        this.grp = parseInt(vars.grp) || 0;
```

```diff
        this.getFavs();
      }
-     // 处理 appMapMode=claim 的情况
-     if (vars.appMapMode == 'claim') {
-       this.viewMode = 'map';
-       this.getClaimedProps();
-     }
      if (!this.scrollElement) {
        this.waiting = false
```

---

已完成代码调整：  
现在无论什么模式，都会**先初始化地图**（mapObj、loadMapBoxScript、initGmap），  
然后再判断 `vars.appMapMode == 'claim'`，如果是，则只执行 `getClaimedProps()` 并 return，  
**后续不会再自动 doSearch**，地图也会正常显示。

如需进一步测试或有其他需求，请继续告知！

---

# 为 claim.coffee 添加单元测试 (2025-06-17 17:01:01)

_**User**_

查看 @claim.coffee  和 @propClaim.coffee 文件中增加的内容，给 @claim.coffee 文件添加 unit test

---

_**Assistant**_

好的，我会按照你的要求进行操作。你的需求是：

1. **查看 `claim.coffee` 和 `propClaim.coffee` 文件中增加的内容**  
2. **为 `claim.coffee` 文件中增加的内容补充单元测试**

### 步骤说明

#### 1. 查看 claim.coffee 和 propClaim.coffee 增加的内容
- 你提供的 `claim.coffee` 和 `propClaim.coffee` 文件内容已经很完整，且都带有详细的注释。
- 主要的业务逻辑和接口都在 `claim.coffee` 的 `Claim` 类方法中实现，`propClaim.coffee` 作为接口层调用这些方法。

#### 2. 检查现有单元测试
- 你已经有 `appweb/src/unitTest/claim/claim.coffee`，里面包含了大量接口和模型的测试用例，覆盖了大部分接口和边界情况。

#### 3. 查找 claim.coffee 新增/未覆盖的内容
- 需要重点关注 `claim.coffee` 中的新增方法或未被单元测试覆盖的逻辑。
- 结合 `propClaim.coffee` 的接口调用，确认是否有遗漏的业务分支或特殊情况未被测试。

#### 4. 为 claim.coffee 增加单元测试
- 按照你的要求，所有新增测试都要带有详细的 JsDoc 注释，解释每个测试块的功能和实现逻辑。

---

### 具体操作

#### 1. claim.coffee 主要方法梳理
- `buildUpdate`
- `saveBaseOnPc`
- `updateTokenIdById`
- `checkSaveClaim`
- `getById`
- `getList`
- `getMapPids`
- `claimedPc`
- `deleteById`
- `checkIfCanClaim`

#### 2. 检查现有单元测试覆盖情况
- 现有测试已经覆盖了：
  - 正常认领/删除
  - 参数缺失
  - 权限校验
  - 认领边界（如 100%/超时/无效状态等）
  - getById、getList、deleteById、checkIfCanClaim 等
- 但部分内部方法如 `buildUpdate`、`getClaimTokenKeys`、`claimedPc`、`getMapPids` 可能未直接测试。

#### 3. 计划补充的测试点
- `buildUpdate` 的参数校验和返回结构
- `getClaimTokenKeys` 的 key 生成逻辑
- `claimedPc` 的用户百分比聚合
- `getMapPids` 的 pid 去重逻辑

---

### 4. 编写单元测试（示例）

我将为 `claim.coffee` 新增/未覆盖的关键方法补充单元测试，并加上详细的 JsDoc 注释。

接下来我会在 `appweb/src/unitTest/claim/claim.coffee` 文件末尾添加如下内容：  
- `describe('ClaimModel.buildUpdate', ...)`
- `describe('ClaimModel.getClaimTokenKeys', ...)`
- `describe('ClaimModel.claimedPc', ...)`
- `describe('ClaimModel.getMapPids', ...)`

每个 describe 块都带有详细注释。

现在开始添加代码。

---

```diff
          should.deepEqual(test.result,result)
          return
+ 
+ ###############################################
+ # 以下为 ClaimModel 新增/未覆盖方法的单元测试
+ ###############################################
+ 
+ ###
+ # @function ClaimModel.buildUpdate
+ # @description 测试 buildUpdate 方法的参数校验和返回结构，确保生成的 updateList 正确。
+ # <AUTHOR>
+ ###
+ describe 'ClaimModel.buildUpdate', ->
+   it 'should return correct updateList for valid input', ->
+     body = {
+       uid: '601a99501d0ae231345daf14',
+       pid: 'TRBW5473866',
+       mergedIds: ['TRBW5473866'],
+       mergedTopId: 'TRBW5473866',
+       sldd: 20240101,
+       saletp: 'Sale',
+       coop: {
+         side: 'coop',
+         m: 'test',
+         people: [
+           {
+             _id: '5f9c25d55d2bb0601b9c141a',
+             role: 'Buyer',
+             source: 'RM Direct',
+             pcOfShr: '100',
+             sourceKey: 'Firsthand'
+           }
+         ]
+       }
+     }
+     updateList = ClaimModel.buildUpdate(body)
+     updateList.should.be.an.Array()
+     updateList.length.should.be.above(0)
+     updateList[0].should.have.properties(['query','set','tokenQuery'])
+ 
+   it 'should return error if people missing sourceKey or pcOfShr', ->
+     body = {
+       uid: '601a99501d0ae231345daf14',
+       pid: 'TRBW5473866',
+       mergedIds: ['TRBW5473866'],
+       mergedTopId: 'TRBW5473866',
+       sldd: 20240101,
+       saletp: 'Sale',
+       coop: {
+         side: 'coop',
+         m: 'test',
+         people: [
+           {
+             _id: '5f9c25d55d2bb0601b9c141a',
+             role: 'Buyer',
+             source: 'RM Direct'
+             # 缺少 pcOfShr/sourceKey
+           }
+         ]
+       }
+     }
+     updateList = ClaimModel.buildUpdate(body)
+     updateList.should.have.property('err')
+ 
+ ###
+ # @function ClaimModel.getClaimTokenKeys
+ # @description 测试 getClaimTokenKeys 方法，确保生成的 token key map 正确。
+ ###
+ describe 'ClaimModel.getClaimTokenKeys', ->
+   it 'should return token keys map with Sale and Rent', ->
+     keysMap = ClaimModel.tokenKeysMap
+     keysMap.should.have.properties(['Sale','Rent'])
+     keysMap['Sale'].should.match(/claimSale/)
+     keysMap['Rent'].should.match(/claimRent/)
+ 
+ ###
+ # @function ClaimModel.getMapPids
+ # @description 测试 getMapPids 方法，确保返回的 pid 数组去重。
+ ###
+ describe 'ClaimModel.getMapPids', ->
+   it 'should return unique pid array', (done) ->
+     @timeout(10000)
+     # 这里用 admin 用户，body 为空，isNoteAdmin true
+     ClaimModel.getMapPids({uid:'601a99501d0ae231345daf14',body:{},isNoteAdmin:true})
+       .then (pids) ->
+         pids.should.be.an.Array()
+         # pid 应该唯一
+         (new Set(pids)).size.should.be.exactly(pids.length)
+         done()
+       .catch (err) ->
+         should.not.exists err
+         done()
+ 
+ ###
+ # @function ClaimModel.claimedPc
+ # @description 测试 claimedPc 方法，确保用户百分比聚合正确。
+ ###
+ describe 'ClaimModel.claimedPc', ->
+   it 'should return userMap with correct structure', (done) ->
+     @timeout(10000)
+     # 取一组已知 pid
+     ClaimModel.getMapPids({uid:'601a99501d0ae231345daf14',body:{},isNoteAdmin:true})
+       .then (pids) ->
+         return ClaimModel.claimedPc(pids,'en')
+       .then (userMap) ->
+         userMap.should.be.an.Object()
+         # userMap[pid][side][uid] 结构
+         Object.keys(userMap).length.should.be.above(0)
+         done()
+       .catch (err) ->
+         should.not.exists err
+         done()
```

---

已为 `claim.coffee` 文件中未直接被接口测试覆盖的关键方法（如 `buildUpdate`、`getClaimTokenKeys`、`getMapPids`、`claimedPc`）补充了详细的单元测试，并且每个测试块都带有 JsDoc 风格注释，解释了测试的功能和实现逻辑。

如需进一步细化或补充其他方法的测试，请告知！

---

_**User**_

@propClaim.coffee 文件新添加的post 增加unit test

---

_**Assistant**_

明白，你的需求是：**为 `propClaim.coffee` 文件中新添加的 POST 接口补充单元测试**，并且要有详细的 JsDoc 注释。

---

## 1. 新增 POST 接口梳理

根据 `appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` 文件，主要 POST 接口有：

- `POST 'edit'` —— 编辑/认领房源
- `POST 'delete'` —— 删除认领
- `POST 'detail'` —— 检查认领详情
- `POST 'list'` —— 获取认领列表
- `POST 'map'` —— 获取认领相关房源的 proplist

这些接口大部分已有一定的单元测试覆盖（见 `appweb/src/unitTest/claim/claim.coffee`），但 `POST 'map'` 相关的接口单测覆盖较少，且未见有专门针对 map 的测试。

---

## 2. 计划补充的测试点

- **POST /propClaim/map**  
  - 权限校验（未登录、普通用户、inreal、admin）
  - 返回结构校验（ok、items 为数组/对象）
  - 参数校验（body 合法/非法）

---

## 3. 测试代码设计

- 路径：`appweb/src/unitTest/claim/claim.coffee`
- 在文件末尾添加 `describe '/propClaim/map'` 测试块
- 每个测试用例都带有详细的 JsDoc 注释

---

## 4. 开始补充代码

我将为 `/propClaim/map` 接口补充单元测试，并加上详细注释。

---

```diff
          should.not.exists err
          done()
+ 
+ ###
+ # @api POST /propClaim/map
+ # @description 测试 propClaim.coffee 新增的 map 接口，覆盖权限、参数、返回结构等。
+ # <AUTHOR>
+ ###
+ describe '/propClaim/map', ->
+   users = ['nouser','user','inreal','admin']
+   tests = [
+     {
+       desc: 'should reject if not logged in',
+       user: 'nouser',
+       post: {},
+       expected: { ok: 0, e: 'Need login' }
+     },
+     {
+       desc: 'should reject if user is not inreal or admin',
+       user: 'user',
+       post: {},
+       expected: { ok: 0, e: 'Not Authorized' }
+     },
+     {
+       desc: 'should return items for inreal',
+       user: 'inreal',
+       post: {},
+       expected: { ok: 1, items: true }
+     },
+     {
+       desc: 'should return items for admin',
+       user: 'admin',
+       post: {},
+       expected: { ok: 1, items: true }
+     },
+     {
+       desc: 'should reject if agent param for inreal',
+       user: 'inreal',
+       post: { agent: '601a99501d0ae231345daf14' },
+       expected: { ok: 0, e: 'Not Authorized' }
+     },
+     {
+       desc: 'should return items for admin with agent param',
+       user: 'admin',
+       post: { agent: '601a99501d0ae231345daf14' },
+       expected: { ok: 1, items: true }
+     }
+   ]
+   tests.forEach (test) ->
+     it test.user+' '+test.desc, (done) ->
+       @timeout(30000)
+       request
+         .post('propClaim/map')
+         .set('Accept', 'application/json')
+         .set('Cookie', ['apsv=appDebug'])
+         .set('Cookie', helpers.userCookies[test.user])
+         .expect('Content-Type', /json/)
+         .send(test.post)
+         .end (err, res) ->
+           if test.expected.ok is 1
+             res.body.ok.should.be.exactly(1)
+             res.body.should.have.property('items')
+             # items 应为数组或对象且非空
+             Object.keys(res.body.items).length.should.be.above(0)
+           else
+             res.body.ok.should.be.exactly(0)
+             res.body.e.should.match(new RegExp(test.expected.e, 'i'))
+           done()
+       return
```

---

已为 `propClaim.coffee` 文件中新添加的 `POST /propClaim/map` 接口补充了详细的单元测试，覆盖了权限校验、参数校验和返回结构等情况，并且每个测试块都带有 JsDoc 风格注释，便于理解和维护。

如需对其他 POST 接口（如 edit、delete、detail、list）补充更细致的单元测试，或有其他接口需要覆盖，请随时告知！

---

