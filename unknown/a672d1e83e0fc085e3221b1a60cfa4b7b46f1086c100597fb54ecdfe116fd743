# 需求 [fix_thumbUrl]

## 反馈

1. Maggie反馈autocomplete返回没有使用新的图片url
2. 发现估价/相似房源/同楼房源返回的列表图片没有使用新的url

## 需求提出人:    Maggie

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-07-22

## 原因

1. autocomplete接口查找房源时,没有添加`tnLH`,`phoP`字段返回
2. 估价/相似房源/同楼房源查找房源时,没有添加`tnLH`,`phoP`字段返回

## 解决办法

1. 房源查找时添加`tnLH`,`phoP`相关字段返回

## 需要修改的fields的地方

1. src/apps/80_sites/AppRM/propRelated/evaluation.coffee -> _MAP_PROP_FIELDS
2. src/libapp/evaluationEngine.coffee -> MAP_PROP_FIELDS
3. src/libapp/propElasticSearch.coffee -> mappingFields
4. src/libapp/properties.coffee -> fieldsBase, listViewBaseFields
5. src/libapp/similarPropEngine.coffee -> MAP_PROP_FIELDS
6. src/model/staticRMlisting.coffee -> DEFAULT_PROP_FIELDS, SITE_MAP_PROP_FIELDS
7. src/apps/80_sites/AppRM/prop/resources.coffee -> '/prop/picUrls'接口

## 影响范围

1. 房源图片url生成

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-22

## online-step

1. 重启server
