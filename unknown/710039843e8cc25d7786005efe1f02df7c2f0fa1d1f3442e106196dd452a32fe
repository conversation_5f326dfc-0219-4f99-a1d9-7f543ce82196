# 需求 [init_setLang]

## 反馈

初次init setting，切换语言后会二次init

## 需求提出人:    Fred
## 修改人：       LiuRui

## 提出日期:      2025-07-17

## 原因

init的时候，切换语言会重新进入页面，此时判断url中有src=setting值，不会再去检查initapp是否存在。之前逻辑只在initapp的状态下会设置initapp参数，所以导致切换语言之后继续设置（认为不是首次init），done也不会增加 initapp 参数，再次进入首页，判断initapp不存在，还是会打开init设置


## 解决办法

如果url中有 src=setting 参数，打开cover后继续检查initapp参数

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-17

## online-step

1. 重启server