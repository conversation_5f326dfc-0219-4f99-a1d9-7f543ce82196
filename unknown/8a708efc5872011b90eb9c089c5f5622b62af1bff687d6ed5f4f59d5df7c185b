# 需求 [fix_picUrls]

## 反馈

1. Rain反馈https://www.realmaster.cn/1.5/SV/photoScrollSwipe?picIndex=0&id=TRBC12281134&isBuilding=true&sid=C12281134&addr=633%20Bay%20St&unt=708&uaddr=CA%3AON%3ATORONTO%3A633%20BAY%20ST&bdrm=1&bthrm=2&fce=%E5%8D%97&sqft=800-899&shortUrlId=wbcA8R8BiT 这个页面也要用我们自己的图片
2. 检查下useTrebPhoto还是否work
3. autocompleteGetNext的返回值很奇怪
```json
{
"_id": "TRBE12289470",
"searchAddr": "2007 83 borough dr",
"thumbUrl": "https://1ms.ca/MLS/TRB/1278/392ee/Need login_cujcT.jpg",
"picUrls": [
        "https://trreb-image.ampre.ca/CMIm8s5ZQoKfSa898lqZviT4k7sAmzbPvWprKQyZ3_8/rs:fit:240:240/wm:.5:so:0:50:.4/wmsh:10/wmt:PHNwYW4gZm9yZWdyb3VuZD0nd2hpdGUnIGZvbnQ9JzY4Jz5UUlVTVFdFTEwgUkVBTFRZIElOQy4sIEJyb2tlcmFnZTwvc3Bhbj4/cb:20250716192406/L3RycmViL2xpc3RpbmdzLzQxLzY0LzM0Lzk5L3AvZDQ2MmRlZTMtODBiNi00M2M3LTgzMzEtMmFiYzA4ZjZhZGE4LmpwZw.jpg"
"img": "https://1ms.ca/MLS/TRB/1278/392ee/Need login_cujcT.jpg"
}
```

## 需求提出人:    Rain

## 修改人：       Luo Xiaowei

## 提出日期:      2025-07-17

## 原因

1. API `prop/picUrls`缺少`phoLH`字段导致
2. useTrebPhoto在manage页面可以设置,对应的url`https://trebphotos.stratusdata.com/Live/Default.ashx?type=ListingPhoto&entityName=Listing&entityID=sid&index=1`对历史房源数据还可以访问。
3. `autocompleteGetNext`当用户没有登陆时,将房源sid设置为了'sid'/'Need Login'

## 解决办法

1. 修改API的查找字段
2. 未登录不应该影响图片url,修改图片url生成与未登录判断顺序

## 影响范围

1. 房源图片url生成

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-17

## online-step

1. 重启server
